package co.bw.hrdc.weblogic.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import co.bw.hrdc.weblogic.util.ApiResponse;

@Component
public class WorkplaceLearningClientFallback implements WorkplaceLearningClient {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkplaceLearningClientFallback.class);
    
    @Override
    public ApiResponse<?> getApplicationByReferenceNumber(String referenceNumber) {
        logger.error("Fallback triggered for getApplicationByReferenceNumber with referenceNumber: {}", referenceNumber);
        logger.error("This indicates that the WORKPLACE-LEARNING service is either down, unreachable, or not responding within the timeout period");
        logger.error("Check if the workplace-learning service is running on port 8091 and registered with Eureka");
        
        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable. Please check service status and try again later.",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> getApplicationById(String id) {
        logger.error("Fallback triggered for getApplicationById with id: {}", id);
        logger.error("This indicates that the WORKPLACE-LEARNING service is either down, unreachable, or not responding within the timeout period");
        logger.error("Check if the workplace-learning service is running on port 8091 and registered with Eureka");

        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable. Please check service status and try again later.",
            null,
            null
        );
    }

        @Override
    public ApiResponse<?> updatePaperlessIdByApplicationId(String applicationId, java.util.Map<String, String> payload) {
        logger.error("Fallback triggered for updatePaperlessIdByApplicationId with applicationId: {}", applicationId);
        logger.error("This indicates that the WORKPLACE-LEARNING service is either down, unreachable, or not responding within the timeout period");
        logger.error("Check if the workplace-learning service is running on port 8091 and registered with Eureka");

        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable. Please check service status and try again later.",
            null,
            null
        );
    }

    @Override
    public ApiResponse<?> updatePaperlessIdToTrainingPlan(String trainingPlanId, java.util.Map<String, String> payload) {
        logger.error("Fallback triggered for updatePaperlessIdToTrainingPlan with trainingPlanId: {}", trainingPlanId);
        logger.error("This indicates that the WORKPLACE-LEARNING service is either down, unreachable, or not responding within the timeout period");
        logger.error("Check if the workplace-learning service is running on port 8091 and registered with Eureka");

        return new ApiResponse<>(
            false,
            "Workplace Learning service is currently unavailable. Please check service status and try again later.",
            null,
            null
        );
    }
}
