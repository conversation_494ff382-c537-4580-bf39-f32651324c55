package bw.org.hrdc.weblogic.workplacelearning.service.impl;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.constants.ApplicationStatus;
import bw.org.hrdc.weblogic.workplacelearning.dto.*;
import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplication;
import bw.org.hrdc.weblogic.workplacelearning.entity.PreApprovalApplicationComments;
import bw.org.hrdc.weblogic.workplacelearning.entity.ParticularOfTraining;
import bw.org.hrdc.weblogic.workplacelearning.entity.ModuleDetails;
import bw.org.hrdc.weblogic.workplacelearning.entity.Batch;
import bw.org.hrdc.weblogic.workplacelearning.entity.EstimatedTrainingCosts;
import bw.org.hrdc.weblogic.workplacelearning.entity.ParticularsOfTrainees;
import bw.org.hrdc.weblogic.workplacelearning.entity.document.DocumentCommon;
import bw.org.hrdc.weblogic.workplacelearning.exception.ApplicationNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.exception.ResourceNotFoundException;
import bw.org.hrdc.weblogic.workplacelearning.mapper.PreApprovalApplicationMapper;
import bw.org.hrdc.weblogic.workplacelearning.repository.BatchRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.PreApprovalApplicationRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.PreApprovalApplicationSpecification;
import bw.org.hrdc.weblogic.workplacelearning.service.IPreApprovalApplicationService;
import bw.org.hrdc.weblogic.workplacelearning.service.PreApprovalApplicationCommentsService;
import bw.org.hrdc.weblogic.workplacelearning.service.document.DocumentService;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import bw.org.hrdc.weblogic.workplacelearning.util.VatNumberGenerator;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.jsoup.*;
import org.jsoup.safety.*;
//import org.jsoup.safety.Whitelist;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpClientErrorException;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;
import java.time.ZoneId;
import java.util.ArrayList;
import jakarta.persistence.criteria.Predicate;
import java.util.HashMap;
import java.util.Objects;
import java.util.Calendar;
import java.text.SimpleDateFormat;
import java.text.ParseException;

/**
 * Implementation of the PreApprovalApplication service.
 */
@Service
@Primary
@AllArgsConstructor
public class PreApprovalApplicationServiceImpl implements IPreApprovalApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(PreApprovalApplicationServiceImpl.class);

    private final PreApprovalApplicationRepository applicationRepository;
    private final PreApprovalApplicationMapper applicationMapper;
    private final CompanyClient companyClient;
    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private PreApprovalApplicationCommentsService commentsService;

    @Autowired
    private DocumentService documentService;

    @Override
    @Transactional
    public PreApprovalApplicationResponseDto createPreApprovalApplication(PreApprovalApplicationDto applicationDto) {
        try {
            logger.info("Creating new pre-approval application");

            if (applicationDto == null) {
                throw new IllegalArgumentException("Application DTO cannot be null");
            }

            // Convert DTO to entity
            PreApprovalApplication application = applicationMapper.toEntity(applicationDto);

            // Validate and set state and status
            validateAndSetStateStatus(application, applicationDto);

            Date now = new Date();
            application.setCreatedDate(now);
            application.setLastModifiedDate(now);

            // Generate and set VAT number
            String vatNumber = VatNumberGenerator.generateVatNumber();
            application.setVatNumber(vatNumber);
            logger.info("Generated VAT number: {}", vatNumber);

            // Save the application
            application = applicationRepository.save(application);
            logger.info("Pre-approval application created with ID: {}", application.getId());

            // Return response
            return PreApprovalApplicationResponseDto.builder()
                    .statusCode("201")
                    .statusMsg("Pre-Approval Application created successfully.")
                    .applicationId(application.getId())
                    .state(application.getState())
                    .status(application.getStatus())
                    .vatNumber(application.getVatNumber())
                    .build();
        } catch (Exception e) {
            logger.error("Error creating pre-approval application: {}", e.getMessage());
            throw new RuntimeException("Failed to create pre-approval application", e);
        }
    }


    @Override
    @Transactional
    public void updatePreApprovalApplication(PreApprovalApplicationDto applicationDto) {
        try {
            logger.info("Updating pre-approval application with ID: {}", applicationDto.getId());

            // Validate input
            if (applicationDto == null || applicationDto.getId() == null) {
                throw new IllegalArgumentException("Application DTO or ID cannot be null");
            }

            // Find existing application
            PreApprovalApplication application = applicationRepository.findById(applicationDto.getId())
                    .orElseThrow(() -> new ApplicationNotFoundException("Application not found with ID: " + applicationDto.getId()));

            // Store current values
            String currentVatNumber = application.getVatNumber();
            Date currentCreatedDate = application.getCreatedDate();
            String currentApplicationNumber = application.getApplicationNumber();
            String currentReferenceNumber = application.getReferenceNumber();

            // Check if this is a draft being converted to a submitted application
            boolean isDraftToSubmitted = Enums.State.DRAFT.name().equals(application.getState()) &&
                                        Enums.State.SUBMITTED.name().equals(applicationDto.getState()) &&
                                        (currentReferenceNumber == null || currentReferenceNumber.isEmpty());

            // If converting from draft to submitted and reference number is provided in DTO, use it
            if (isDraftToSubmitted && applicationDto.getReferenceNumber() != null && !applicationDto.getReferenceNumber().isEmpty()) {
                logger.info("Converting draft to submitted application with provided reference number: {}", applicationDto.getReferenceNumber());
                currentReferenceNumber = applicationDto.getReferenceNumber();
            }

            // Validate and set state and status
            validateAndSetStateStatus(application, applicationDto);

            // Update other fields from DTO
            applicationMapper.updateEntityFromDto(applicationDto, application);

            // Ensure critical fields are preserved
            application.setVatNumber(currentVatNumber);
            application.setCreatedDate(currentCreatedDate);
            application.setLastModifiedDate(new Date());
            application.setApplicationNumber(currentApplicationNumber);
            application.setReferenceNumber(currentReferenceNumber);

            // Save the updated application
            applicationRepository.save(application);
            logger.info("Pre-approval application updated successfully with ID: {}", application.getId());
        } catch (ApplicationNotFoundException e) {
            logger.error("Application not found: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Error updating pre-approval application: {}", e.getMessage());
            throw new RuntimeException("Failed to update pre-approval application", e);
        }
    }

    /**
     * Validates and sets the state and status of an application.
     * Ensures valid state transitions and status values.
     */
    private void validateAndSetStateStatus(PreApprovalApplication application, PreApprovalApplicationDto dto) {
        String state = dto.getState();
        String status = dto.getStatus();

        // Validate state
        if (state != null && !isValidState(state)) {
            throw new IllegalArgumentException("Invalid state: " + state);
        }

        // Validate status
        if (status != null && !isValidStatus(status)) {
            throw new IllegalArgumentException("Invalid status: " + status);
        }

        // Only set state and status if they are not null in the DTO
        if (state != null) {
            application.setState(state);
        }
        
        if (status != null) {
            application.setStatus(status);
        }

        logger.info("State is now {} and status is now {}", application.getState(), application.getStatus());
    }

    /**
     * Checks if a state value is valid.
     */
    private boolean isValidState(String state) {
        try {
            Enums.State.valueOf(state);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * Checks if a status value is valid.
     */
    private boolean isValidStatus(String status) {
        try {
            Enums.Status.valueOf(status);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    @Override
    @Transactional
    public void softDeletePreApprovalApplication(UUID id) {
        logger.info("Soft deleting pre-approval application with ID: {}", id);

        PreApprovalApplication application = applicationRepository.findById(id)
                .orElseThrow(() -> new ApplicationNotFoundException("Application not found with ID: " + id));

        // Set deleted flag instead of actually deleting
        application.setDeleted(true);
        application.setDeletedDate(new Date());

        applicationRepository.save(application);
        logger.info("Pre-approval application soft deleted with ID: {}", id);
    }


    @Override
    public Optional<PreApprovalApplication> getPreApprovalApplicationEntity(UUID id) {
        return applicationRepository.findById(id);
    }

    @Override
    public int updateApplicationAssignedUser(UUID id, Enums.UserRoles role, String userId) {
        return applicationRepository.updateApplicationAssignedUser(id, role.name(), userId);
    }



    @Override
    @Transactional
    public int updateApplicationStatus(UUID applicationId, String role, String action, String newAssignee) {
        logger.info("Updating status for application ID: {}, role: {}, action: {}, newAssignee: {}",
            applicationId, role, action, newAssignee);
            
        return applicationRepository.changeApplicationStatus(applicationId, role, action, newAssignee);
    }

    @Override
    public String sanitizeHtml(String content) {
        if (content == null) {
            return "";
        }
        // Use Jsoup to sanitize HTML content
        return Jsoup.clean(content, Whitelist.basic());
    }

    @Override
    public Page<PreApprovalApplication> findAll(Specification<PreApprovalApplication> spec, Pageable pageable) {
        logger.info("Finding applications with specification and pagination");
        return applicationRepository.findAll(spec, pageable);
    }

    @Override
    public Page<PreApprovalApplicationListDto> getAllCompanyApplications(UUID companyId, Pageable pageable) {
        logger.info("Fetching pre-approval applications for company: {}", companyId);

        Specification<PreApprovalApplication> spec = (root, query, cb) ->
            cb.and(
                cb.equal(root.get("organisationId"), companyId),
                cb.or(
                    cb.isNull(root.get("deleted")),
                    cb.equal(root.get("deleted"), false)
                )
            );

        Page<PreApprovalApplication> applications = applicationRepository.findAll(spec, pageable);

        return applications.map(app -> {
            PreApprovalApplicationListDto dto = new PreApprovalApplicationListDto();

            // Set basic application fields
            dto.setId(app.getId());
            dto.setOrganisationId(app.getOrganisationId());
            dto.setStatus(app.getStatus());
            dto.setState(app.getState());
            dto.setReasonForTraining(app.getReasonForTraining());
            dto.setCourseTitle(app.getCourseTitle());
            dto.setTrainingProvider(app.getTrainingProvider());
            dto.setVatNumber(app.getVatNumber());
            dto.setCreatedDate(app.getCreatedDate() != null ?
                app.getCreatedDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null);

            // Set assignment fields
            dto.setAssignedAgent(app.getAssignedAgent());
            dto.setAssignedAgentLead(app.getAssignedAgentLead());
            dto.setAssignedOfficerLead(app.getAssignedOfficerLead());
            dto.setAssignedOfficer(app.getAssignedOfficer());
            dto.setAssignedManager(app.getAssignedManager());

            // Set assignedTo based on state and status
            if(app.getState().equals(Enums.State.IN_APPROVAL.name()) && !app.getStatus().equals(Enums.Status.CHANGE_REQUEST.name())){
                dto.setAssignedTo(app.getAssignedManager());
            } else if(app.getState().equals(Enums.State.IN_REVIEW.name()) && !app.getStatus().equals(Enums.Status.CHANGE_REQUEST.name())){
                dto.setAssignedTo(app.getAssignedOfficer());
            } else if(app.getState().equals(Enums.State.IN_PROCESSING.name()) && !app.getStatus().equals(Enums.Status.CHANGE_REQUEST.name())){
                dto.setAssignedTo(app.getAssignedAgent());
            } else if(app.getState().equals(Enums.State.SUBMITTED.name()) && !app.getStatus().equals(Enums.Status.CHANGE_REQUEST.name())){
                dto.setAssignedTo(app.getAssignedAgentLead());
            } else {
                dto.setAssignedTo(null);
            }

            // Fetch and set company details
            try {
                ApiResponse<?> companyResponse = companyClient.fetchCompanyById(app.getOrganisationId().toString());
                if (companyResponse != null && companyResponse.isStatus() && companyResponse.getData() != null) {
                    Map<String, Object> companyData = (Map<String, Object>) companyResponse.getData();
                    dto.setCompanyName((String) companyData.get("name"));
                    dto.setPhysicalAddress((String) companyData.get("physicalAddress"));
                    dto.setTelephoneNumber((String) companyData.get("telephoneNumber"));
                    dto.setFaxNumber((String) companyData.get("faxNumber"));
                    dto.setEmail((String) companyData.get("email"));
                }
            } catch (Exception e) {
                logger.error("Error fetching company details for application {}: {}", app.getId(), e.getMessage());
            }

            return dto;
        });
    }

    @Override
    public PreApprovalApplicationDto getApplicationByApplicationNumber(String applicationNumber) {
        logger.info("Fetching pre-approval application by ID: {}", applicationNumber);

        PreApprovalApplication application = applicationRepository.findById(UUID.fromString(applicationNumber))
                .orElseThrow(() -> new ResourceNotFoundException("PreApprovalApplication not found with id: " + applicationNumber));

        return populateApplicationDto(application);
    }


    @Override
    public PreApprovalApplicationDto getApplicationByReferenceNumber(String referenceNumber) {
        logger.info("Fetching pre-approval application by reference number: {}", referenceNumber);

        PreApprovalApplication application = applicationRepository.findByReferenceNumber(referenceNumber)
                .orElseThrow(() -> new ResourceNotFoundException("PreApprovalApplication not found with reference number: " + referenceNumber));

        return populateApplicationDto(application);
    }

    @Override
    public PreApprovalApplication fetchApplicationByReferenceNumber(String referenceNumber) {
        Optional<PreApprovalApplication> application = applicationRepository.findByReferenceNumber(referenceNumber);
        if(application.isPresent()){
            return application.get();
        }
        return null;
    }

    /**
     * Helper method to populate a PreApprovalApplicationDto with all related data
     * 
     * @param application the PreApprovalApplication entity
     * @return fully populated PreApprovalApplicationDto
     */
    private PreApprovalApplicationDto populateApplicationDto(PreApprovalApplication application) {
        PreApprovalApplicationDto dto = applicationMapper.toDto(application);

        try {
            // 1. Fetch company details (which includes employee information)
            if (application.getOrganisationId() != null) {
                ApiResponse<?> companyResponse = companyClient.fetchCompanyById(application.getOrganisationId().toString());
                if (companyResponse != null && companyResponse.isStatus() && companyResponse.getData() != null) {
                    Map<String, Object> companyData = (Map<String, Object>) companyResponse.getData();
                    dto.setCompany(companyData);
                } else {
                    logger.warn("Company data not available for PreApprovalApplication id: {}, Company id: {}",
                        application.getId(), application.getOrganisationId());
                }
            }
            
            // 2. Set Particular of Training details
            if (application.getParticularOfTrainings() != null && !application.getParticularOfTrainings().isEmpty()) {
                List<ParticularOfTrainingDto> trainingDtos = application.getParticularOfTrainings().stream()
                    .map(training -> {
                        ParticularOfTrainingDto trainingDto = new ParticularOfTrainingDto();
                        trainingDto.setId(training.getId());
                        trainingDto.setProductRelatedTraining(training.getProductRelatedTraining());
                        trainingDto.setOutsourcedTraining(training.getOutsourcedTraining());
                        trainingDto.setNonCitizenEmployee(training.getNonCitizenEmployee());
                        trainingDto.setIsTrainingLocal(training.getIsTrainingLocal());
                        return trainingDto;
                    })
                    .collect(Collectors.toList());
                dto.setParticularOfTrainings(trainingDtos);
            }

            // 3. Set Module Details
            if (application.getModuleDetails() != null && !application.getModuleDetails().isEmpty()) {
                List<ModuleDetailsDto> moduleDtos = application.getModuleDetails().stream()
                    .map(module -> {
                        ModuleDetailsDto moduleDto = new ModuleDetailsDto();
                        moduleDto.setId(module.getId());
                        moduleDto.setModuleName(module.getModuleName());
                        moduleDto.setExpectedCompetencies(module.getExpectedCompetencies());
                        moduleDto.setModuleReferenceId(module.getModuleReferenceId());
                        return moduleDto;
                    })
                    .collect(Collectors.toList());
                dto.setModuleDetails(moduleDtos);
            }

            // 4. Set Estimated Training Costs
            if (application.getEstimatedTrainingCosts() != null && !application.getEstimatedTrainingCosts().isEmpty()) {
                List<EstimatedTrainingCostsDto> costDtos = application.getEstimatedTrainingCosts().stream()
                    .map(cost -> {
                        EstimatedTrainingCostsDto costDto = new EstimatedTrainingCostsDto();
                        costDto.setId(cost.getId());
                        costDto.setItemDescription(cost.getItemDescription());
                        costDto.setAmount(cost.getAmount());
                        return costDto;
                    })
                    .collect(Collectors.toList());
                dto.setEstimatedTrainingCosts(costDtos);
            }

            // 5. Calculate and set totals
            if (application.getEstimatedTrainingCosts() != null && !application.getEstimatedTrainingCosts().isEmpty()) {
                double subtotal = application.getEstimatedTrainingCosts().stream()
                    .mapToDouble(EstimatedTrainingCosts::getAmount)
                    .sum();
                dto.setSubtotal(BigDecimal.valueOf(subtotal));
                // Assuming VAT is 14%
                dto.setTotal(BigDecimal.valueOf(subtotal * 1.14));
            }

            // 6. Set basic application fields
            dto.setVatNumber(application.getVatNumber());
            dto.setStatus(application.getStatus());
            dto.setState(application.getState());
            dto.setReasonForTraining(application.getReasonForTraining());
            dto.setCourseTitle(application.getCourseTitle());
            dto.setTrainingProvider(application.getTrainingProvider());
            dto.setCityOfTraining(application.getCityOfTraining());
            dto.setTrainingStartDate(application.getTrainingStartDate());
            dto.setTrainingEndDate(application.getTrainingEndDate());
            dto.setAccreditationEvidence(application.getAccreditationEvidence());

            // 7. Set assignment information
            dto.setAssignedAgent(application.getAssignedAgent());
            dto.setAssignedAgentLead(application.getAssignedAgentLead());
            dto.setAssignedOfficerLead(application.getAssignedOfficerLead());
            dto.setAssignedOfficer(application.getAssignedOfficer());
            dto.setAssignedManager(application.getAssignedManager());

            // 8. Set audit fields
            dto.setCreatedDate(application.getCreatedDate());
            dto.setLastModifiedDate(application.getLastModifiedDate());
            dto.setCreatedBy(application.getCreatedBy());
            dto.setLastModifiedBy(application.getLastModifiedBy());

        } catch (Exception e) {
            logger.error("Error fetching company details: {}", e.getMessage());
        }

        return dto;
    }

    @Override
    public Optional<PreApprovalApplication> getPreApprovalApplicationEntityByVatNumber(String vatNumber) {
        logger.info("Fetching pre-approval application by VAT number: {}", vatNumber);
        return applicationRepository.findByVatNumber(vatNumber);
    }

    @Override
    public long count(Specification<PreApprovalApplication> spec) {
        return applicationRepository.count(spec);
    }

    @Override
    public Map<String, Object> getApplicationsByRole(
            String role, String userId, UUID companyId, 
            String applicationStatus, String applicationState, String trainingProvider,
            Date startDate, Date endDate, String vatNumber, String referenceNumber,
            String companyName, String courseTitle, String assignee, 
            String search, int pageNumber, int size) {
        
        logger.info("Service: Fetching applications with criteria - role: {}, userId: {}, companyId: {}, status: {}, state: {}, assignee: {}, search: {}",
                role, userId, companyId, applicationStatus, applicationState, assignee, search);
        
        // Build base specification for role and company filtering only
        Specification<PreApprovalApplication> baseRoleSpec = buildBaseRoleSpecification(role, userId, companyId);
        
        // Build full specification with all search criteria
        Specification<PreApprovalApplication> fullSpec = buildApplicationSpecification(
                role, userId, companyId, applicationStatus, applicationState, trainingProvider, 
                startDate, endDate, vatNumber, companyName, courseTitle, assignee, search);
        
        // Execute query with pagination using the full specification
        Pageable pageable = PageRequest.of(pageNumber, size, Sort.by("createdDate").descending());
        Page<PreApprovalApplication> applications = findAll(fullSpec, pageable);
        
        if (applications.isEmpty()) {
            return createEmptyResponse(pageNumber, size);
        }
        
        // Convert entities to DTOs
        List<PreApprovalApplicationListDto> applicationDtos = applications.getContent().stream()
                .map(this::convertToListDto)
                .collect(Collectors.toList());
        
        // Build response
        Map<String, Object> response = new HashMap<>();
        
        // Create metadata with counts based on the base role specification only
        Map<String, Object> metadata = createMetadata(applications, baseRoleSpec);
        response.put("metadata", metadata);
        response.put("content", applicationDtos);
        
        return response;
    }

    /**
     * Builds JPA specification for filtering applications
     */
    private Specification<PreApprovalApplication> buildApplicationSpecification(
            String role, String userId, UUID companyId, String applicationStatus,
            String applicationState, String trainingProvider, Date startDate, Date endDate, String vatNumber,
            String companyName, String courseTitle, String assignee, String search) {
        
        // First get the base role specification
        Specification<PreApprovalApplication> baseSpec = buildBaseRoleSpecification(role, userId, companyId);
        
        // Then add additional filters
        return (root, query, cb) -> {
            // Get predicates from base specification
            Predicate basePredicate = baseSpec.toPredicate(root, query, cb);
            
            // Create new list for additional predicates
            List<Predicate> additionalPredicates = new ArrayList<>();
            
            // Add status filter
            if (applicationStatus != null && !applicationStatus.isEmpty()) {
                String[] statuses = applicationStatus.split(",");
                if (statuses.length > 1) {
                    // For multiple statuses, create an OR condition with partial matching
                    Predicate statusPredicate = cb.disjunction();
                    for (String status : statuses) {
                        String trimmedStatus = status.trim().toLowerCase();
                        statusPredicate = cb.or(statusPredicate,
                            cb.like(cb.lower(root.get("status")), "%" + trimmedStatus + "%"));
                    }
                    additionalPredicates.add(statusPredicate);
                } else {
                     // For single status, use partial match with case insensitive comparison
                     additionalPredicates.add(cb.like(cb.lower(root.get("status")),
                        "%" + applicationStatus.toLowerCase() + "%"));
                }
            }
            
            // Add state filter
            if (applicationState != null && !applicationState.isEmpty()) {
                String[] states = applicationState.split(",");
                if (states.length > 1) {
                    // For multiple states, create an OR condition with partial matching
                    Predicate statePredicate = cb.disjunction();
                    for (String state : states) {
                        String trimmedState = state.trim().toLowerCase();
                        statePredicate = cb.or(statePredicate,
                            cb.like(cb.lower(root.get("state")), "%" + trimmedState + "%"));
                    }
                    additionalPredicates.add(statePredicate);
                } else {
                    // For single state, use exact match instead of partial match
                    additionalPredicates.add(cb.equal(root.get("state"), applicationState));
                }
            }
            
            // Add training provider filter
            if (trainingProvider != null && !trainingProvider.isEmpty()) {
                String[] providers = trainingProvider.split(",");
                if (providers.length > 1) {
                    // For multiple training providers, create an OR condition
                    Predicate providerPredicate = cb.disjunction();
                    for (String provider : providers) {
                        String trimmedProvider = provider.trim();
                        providerPredicate = cb.or(providerPredicate,
                            cb.like(cb.lower(root.get("trainingProvider")),
                                "%" + trimmedProvider.toLowerCase() + "%"));
                    }
                    additionalPredicates.add(providerPredicate);
                } else {
                    // For single training provider, use LIKE for partial matches
                    additionalPredicates.add(cb.like(cb.lower(root.get("trainingProvider")),
                        "%" + trainingProvider.toLowerCase() + "%"));
                }
            }
            
            // Add date range filters
            if (startDate != null) {
                // Set time to beginning of day (00:00:00)
                Calendar startCal = Calendar.getInstance();
                startCal.setTime(startDate);
                startCal.set(Calendar.HOUR_OF_DAY, 0);
                startCal.set(Calendar.MINUTE, 0);
                startCal.set(Calendar.SECOND, 0);
                startCal.set(Calendar.MILLISECOND, 0);
                Date startOfDay = startCal.getTime();
                
                additionalPredicates.add(cb.greaterThanOrEqualTo(root.get("createdDate"), startOfDay));
                logger.debug("Filtering applications created on or after: {}", startOfDay);
            }

            if (endDate != null) {
                // Set time to end of day (23:59:59.999)
                Calendar endCal = Calendar.getInstance();
                endCal.setTime(endDate);
                endCal.set(Calendar.HOUR_OF_DAY, 23);
                endCal.set(Calendar.MINUTE, 59);
                endCal.set(Calendar.SECOND, 59);
                endCal.set(Calendar.MILLISECOND, 999);
                Date endOfDay = endCal.getTime();
                
                additionalPredicates.add(cb.lessThanOrEqualTo(root.get("createdDate"), endOfDay));
                logger.debug("Filtering applications created on or before: {}", endOfDay);
            }
            
            // Add VAT number filter
            if (vatNumber != null && !vatNumber.isEmpty()) {
                additionalPredicates.add(cb.like(cb.lower(root.get("vatNumber")),
                    "%" + vatNumber.toLowerCase() + "%"));
            }
            
            // Add course title filter
            if (courseTitle != null && !courseTitle.isEmpty()) {
                additionalPredicates.add(cb.like(cb.lower(root.get("courseTitle")),
                    "%" + courseTitle.toLowerCase() + "%"));
            }
            
            // Add assignee search logic
            if (assignee != null && !assignee.isEmpty()) {
                try {
                    // Use the search API to find users by username
                    ApiResponse<?> userResponse = companyClient.searchUsersByUsername(assignee);
                    logger.info("User Search API Response for '{}': {}", assignee, userResponse);
                    if (userResponse != null && userResponse.isStatus() && userResponse.getData() != null) {
                        List<Map<String, Object>> users = (List<Map<String, Object>>) userResponse.getData();
                        if (!users.isEmpty()) {
                            List<String> userIds = users.stream()
                                .map(user -> {
                                    try {
                                        String id = user.get("userId").toString();
                                        logger.debug("Found matching user: {} with ID: {}",
                                            user.get("userName"), id);
                                        return id;
                                    } catch (Exception e) {
                                        logger.warn("Invalid user ID format for user: {}",
                                            user.get("userName"), e);
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                            if (!userIds.isEmpty()) {
                                logger.info("Found {} matching users for assignee: {} -> User IDs: {}",
                                    userIds.size(), assignee, userIds);
                                // Create OR conditions for all assigned fields
                                Predicate assigneePredicate = cb.or(
                                    root.get("assignedAgent").in(userIds),
                                    root.get("assignedAgentLead").in(userIds),
                                    root.get("assignedOfficer").in(userIds),
                                    root.get("assignedOfficerLead").in(userIds),
                                    root.get("assignedManager").in(userIds)
                                );
                                additionalPredicates.add(assigneePredicate);
                            } else {
                                // No valid user IDs found - return no results
                                logger.warn("No valid user IDs found for assignee: {}", assignee);
                                additionalPredicates.add(cb.equal(cb.literal(1), 0));
                            }
                        } else {
                            // No users found - return no results
                            logger.warn("No users found matching assignee: {}", assignee);
                            additionalPredicates.add(cb.equal(cb.literal(1), 0));
                        }
                    } else {
                        logger.warn("User search API returned null or invalid response for assignee: {}",
                            assignee);
                        additionalPredicates.add(cb.equal(cb.literal(1), 0));
                    }
                } catch (Exception e) {
                    logger.error("Error searching for user with assignee '{}': {}",
                        assignee, e.getMessage(), e);
                    additionalPredicates.add(cb.equal(cb.literal(1), 0));
                }
            }
            
            // Add company name search logic
            if (companyName != null && !companyName.isEmpty()) {
                try {
                    // Use the search API with proper error handling
                    ApiResponse<?> companyResponse = companyClient.searchCompanyByName(companyName);
                    logger.info("Company Search API Response for '{}': {}", companyName, companyResponse);
                    
                    if (companyResponse != null && companyResponse.isStatus() && companyResponse.getData() != null) {
                        List<Map<String, Object>> companies = (List<Map<String, Object>>) companyResponse.getData();
                        
                        if (!companies.isEmpty()) {
                            List<UUID> companyIds = companies.stream()
                                .map(company -> {
                                    try {
                                        String id = company.get("organizationId").toString();
                                        logger.debug("Found matching company: {} with UUID: {}",
                                            company.get("name"), id);
                                        return UUID.fromString(id);
                                    } catch (Exception e) {
                                        logger.warn("Invalid company UUID format for company: {}",
                                            company.get("name"), e);
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                            
                            if (!companyIds.isEmpty()) {
                                logger.info("Found {} matching companies for name: {}",
                                    companyIds.size(), companyName);
                                additionalPredicates.add(root.get("organisationId").in(companyIds));
                            } else {
                                // No valid company IDs found - return no results
                                logger.warn("No valid company IDs found for name: {}", companyName);
                                additionalPredicates.add(cb.equal(cb.literal(1), 0));
                            }
                        } else {
                            // No companies found - return no results
                            logger.warn("No companies found matching name: {}", companyName);
                            additionalPredicates.add(cb.equal(cb.literal(1), 0));
                        }
                    } else {
                        logger.warn("Company search API returned null or invalid response for name: {}",
                            companyName);
                        additionalPredicates.add(cb.equal(cb.literal(1), 0));
                    }
                } catch (Exception e) {
                    logger.error("Error searching for company with name '{}': {}",
                        companyName, e.getMessage(), e);
                    additionalPredicates.add(cb.equal(cb.literal(1), 0));
                }
            }
            
            // Add unified search functionality
            if (search != null && !search.isEmpty() && !search.contains(":")) {
                logger.info("Processing unified search with term: {}", search);
                String searchTerm = "%" + search.toLowerCase() + "%";
                
                // Create a disjunction (OR) for all searchable fields
                Predicate searchPredicate = cb.disjunction();

                // Add reference number search to unified search as well
                searchPredicate = cb.or(searchPredicate,
                    cb.like(cb.lower(root.get("referenceNumber")), searchTerm));
                
                // Search in course title
                searchPredicate = cb.or(searchPredicate,
                    cb.like(cb.lower(root.get("courseTitle")), searchTerm));
                
                // Search in training provider
                searchPredicate = cb.or(searchPredicate,
                    cb.like(cb.lower(root.get("trainingProvider")), searchTerm));
                
                // Search in VAT number
                searchPredicate = cb.or(searchPredicate,
                    cb.like(cb.lower(root.get("vatNumber")), searchTerm));

                // Add date search - try to parse the search term as a date
                try {
                    // Try multiple date formats
                    SimpleDateFormat[] dateFormats = {
                        new SimpleDateFormat("yyyy-MM-dd"),
                        new SimpleDateFormat("dd-MM-yyyy"),
                        new SimpleDateFormat("MM/dd/yyyy"),
                        new SimpleDateFormat("dd/MM/yyyy"),
                        new SimpleDateFormat("yyyy/MM/dd")
                    };
                    
                    Date searchDate = null;
                    for (SimpleDateFormat format : dateFormats) {
                        try {
                            format.setLenient(false); 
                            searchDate = format.parse(search.trim());
                            logger.info("Successfully parsed date '{}' using format '{}'", 
                                search.trim(), format.toPattern());
                            break;
                        } catch (ParseException e) {
                            // Try next format
                        }
                    }
                    
                    if (searchDate != null) {
                        // Set time to beginning of day (00:00:00)
                        Calendar startCal = Calendar.getInstance();
                        startCal.setTime(searchDate);
                        startCal.set(Calendar.HOUR_OF_DAY, 0);
                        startCal.set(Calendar.MINUTE, 0);
                        startCal.set(Calendar.SECOND, 0);
                        startCal.set(Calendar.MILLISECOND, 0);
                        Date startOfDay = startCal.getTime();
                        
                        // Set time to end of day (23:59:59.999)
                        Calendar endCal = Calendar.getInstance();
                        endCal.setTime(searchDate);
                        endCal.set(Calendar.HOUR_OF_DAY, 23);
                        endCal.set(Calendar.MINUTE, 59);
                        endCal.set(Calendar.SECOND, 59);
                        endCal.set(Calendar.MILLISECOND, 999);
                        Date endOfDay = endCal.getTime();
                        
                        logger.info("Searching for date: {} (from {} to {})", 
                            new SimpleDateFormat("yyyy-MM-dd").format(searchDate), startOfDay, endOfDay);
                        
                        // Add date search to the predicate - match any date within the day
                        searchPredicate = cb.or(searchPredicate,
                            cb.between(root.get("createdDate"), startOfDay, endOfDay));
                    }
                } catch (Exception e) {
                    logger.warn("Search term '{}' is not a valid date format: {}", search, e.getMessage());
                }

                // Check if search term matches any Status enum value
                try {
                    // Try to parse the search term as a Status enum
                    Enums.Status statusEnum = Enums.Status.valueOf(search.trim().toUpperCase());
                    logger.info("Found exact Status enum match: {}", statusEnum.name());
                    searchPredicate = cb.or(searchPredicate, 
                        cb.equal(root.get("status"), statusEnum.name()));
                } catch (IllegalArgumentException e) {
                    // Not a valid Status enum, try State enum
                    try {
                        Enums.State stateEnum = Enums.State.valueOf(search.trim().toUpperCase());
                        logger.info("Found exact State enum match: {}", stateEnum.name());
                        searchPredicate = cb.or(searchPredicate,
                            cb.equal(root.get("state"), stateEnum.name()));
                    } catch (IllegalArgumentException ex) {
                        // Not a valid State enum either, try partial matching
                        logger.info("No exact enum match, trying partial matches for enum values");
                        Predicate statusStatePredicate = cb.disjunction();
                        String searchUpper = search.toUpperCase();
                        
                        // Check for partial matches in Status enum values
                        for (Enums.Status status : Enums.Status.values()) {
                            if (status.name().contains(searchUpper) || 
                                searchUpper.contains(status.name())) {
                                logger.info("Found partial Status enum match: {}", status.name());
                                statusStatePredicate = cb.or(statusStatePredicate,
                                    cb.equal(root.get("status"), status.name()));
                            }
                        }
                        
                        // Check for partial matches in State enum values
                        for (Enums.State state : Enums.State.values()) {
                            if (state.name().contains(searchUpper) || 
                                searchUpper.contains(state.name())) {
                                logger.info("Found partial State enum match: {}", state.name());
                                statusStatePredicate = cb.or(statusStatePredicate,
                                    cb.equal(root.get("state"), state.name()));
                            }
                        }
                        
                        // Add the combined status/state predicate to the search predicate
                        searchPredicate = cb.or(searchPredicate, statusStatePredicate);
                    }
                }
                
                // Add company search via API
                try {
                    ApiResponse<?> companyResponse = companyClient.searchCompanyByName(search);
                    if (companyResponse != null && companyResponse.isStatus() && companyResponse.getData() != null) {
                        List<Map<String, Object>> companies = (List<Map<String, Object>>) companyResponse.getData();
                        if (!companies.isEmpty()) {
                            List<UUID> companyIds = companies.stream()
                                .map(company -> {
                                    try {
                                        return UUID.fromString(company.get("organizationId").toString());
                                    } catch (Exception e) {
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                            
                            if (!companyIds.isEmpty()) {
                                searchPredicate = cb.or(searchPredicate,
                                    root.get("organisationId").in(companyIds));
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.warn("Error searching companies with term '{}': {}", search, e.getMessage());
                }
                
                // Add assignee search via API
                try {
                    ApiResponse<?> userResponse = companyClient.searchUsersByUsername(search);
                    if (userResponse != null && userResponse.isStatus() && userResponse.getData() != null) {
                        List<Map<String, Object>> users = (List<Map<String, Object>>) userResponse.getData();
                        if (!users.isEmpty()) {
                            List<String> userIds = users.stream()
                                .map(user -> {
                                    try {
                                        return user.get("userId").toString();
                                    } catch (Exception e) {
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                            
                            if (!userIds.isEmpty()) {
                                Predicate assigneePredicate = cb.or(
                                    root.get("assignedAgent").in(userIds),
                                    root.get("assignedAgentLead").in(userIds),
                                    root.get("assignedOfficer").in(userIds),
                                    root.get("assignedOfficerLead").in(userIds),
                                    root.get("assignedManager").in(userIds)
                                );
                                searchPredicate = cb.or(searchPredicate, assigneePredicate);
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.warn("Error searching users with term '{}': {}", search, e.getMessage());
                }
                
                additionalPredicates.add(searchPredicate);
            }
            
            // Combine base predicate with additional predicates
            if (!additionalPredicates.isEmpty()) {
                return cb.and(
                    basePredicate,
                    cb.and(additionalPredicates.toArray(new Predicate[0]))
                );
            } else {
                return basePredicate;
            }
        };
    }

    /**
     * Builds a base specification that only includes role and company filtering
     */
    private Specification<PreApprovalApplication> buildBaseRoleSpecification(
            String role, String userId, UUID companyId) {
        
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // Base condition: not deleted
            predicates.add(cb.equal(root.get("deleted"), false));
            
            // If companyId is provided, show all data for that company
            if (companyId != null) {
                predicates.add(cb.equal(root.get("organisationId"), companyId));
            } else {
                // Role-based filtering
                if (role != null && userId != null) {
                    switch (role.toUpperCase()) {
                        case "AGENT":
                            // Show applications assigned to this agent (excluding drafts)
                            predicates.add(cb.and(
                                cb.equal(root.get("assignedAgent"), userId),
                                cb.notEqual(root.get("state"), "DRAFT"),
                                cb.equal(root.get("state"), "IN_PROCESSING")
                            ));
                            break;
                        case "AGENT_LEAD":
                            // For AGENT_LEAD, show all applications except drafts
                            predicates.add(cb.notEqual(root.get("state"), "DRAFT"));
                            break;
                        case "OFFICER":
                            // Show applications assigned to this officer (excluding drafts)
                            predicates.add(cb.and(
                                cb.equal(root.get("assignedOfficer"), userId),
                                cb.notEqual(root.get("state"), "DRAFT"),
                                cb.equal(root.get("state"), "IN_REVIEW")
                            ));
                            break;
                        case "OFFICER_LEAD":
                            // For OFFICER_LEAD, show all applications except drafts
                            predicates.add(cb.and(
                                cb.notEqual(root.get("state"), "DRAFT"),
                                cb.equal(root.get("state"), "IN_REVIEW")
                            ));
                            break;
                        case "MANAGER":
                            // Show applications assigned to this manager (excluding drafts)
                            predicates.add(cb.and(
                                cb.notEqual(root.get("state"), "DRAFT"),
                                cb.equal(root.get("state"), "IN_APPROVAL")
                            ));
                            break;
                        default:
                            // For invalid roles, add a condition that will return no results
                            predicates.add(cb.equal(cb.literal(1), 0));
                    }
                }
            }
            
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * Converts a PreApprovalApplication entity to a PreApprovalApplicationListDto
     */
    private PreApprovalApplicationListDto convertToListDto(PreApprovalApplication application) {
        PreApprovalApplicationListDto dto = new PreApprovalApplicationListDto();
        
        // Set basic application fields
        dto.setId(application.getId());
        dto.setOrganisationId(application.getOrganisationId());
        dto.setStatus(application.getStatus());
        dto.setState(application.getState());
        dto.setApplicationNumber(application.getApplicationNumber());
        dto.setReferenceNumber(application.getReferenceNumber());
        dto.setReasonForTraining(application.getReasonForTraining());
        dto.setCourseTitle(application.getCourseTitle());
        dto.setTrainingProvider(application.getTrainingProvider());
        dto.setVatNumber(application.getVatNumber());
        dto.setCreatedDate(application.getCreatedDate() != null ?
            application.getCreatedDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null);
        
        // Set assignment fields
        dto.setAssignedAgent(application.getAssignedAgent());
        dto.setAssignedAgentLead(application.getAssignedAgentLead());
        dto.setAssignedOfficerLead(application.getAssignedOfficerLead());
        dto.setAssignedOfficer(application.getAssignedOfficer());
        dto.setAssignedManager(application.getAssignedManager());
        
        // Set assignedTo based on state and status
        if(application.getState().equals(Enums.State.IN_APPROVAL.name()) && !application.getStatus().equals(Enums.Status.CHANGE_REQUEST.name())){
            dto.setAssignedTo(application.getAssignedManager());
        } else if(application.getState().equals(Enums.State.IN_REVIEW.name()) && !application.getStatus().equals(Enums.Status.CHANGE_REQUEST.name())){
            dto.setAssignedTo(application.getAssignedOfficer());
        } else if(application.getState().equals(Enums.State.IN_PROCESSING.name()) && !application.getStatus().equals(Enums.Status.CHANGE_REQUEST.name())){
            dto.setAssignedTo(application.getAssignedAgent());
        } else if(application.getState().equals(Enums.State.SUBMITTED.name()) && !application.getStatus().equals(Enums.Status.CHANGE_REQUEST.name())){
            dto.setAssignedTo(application.getAssignedAgentLead());
        } else {
            dto.setAssignedTo(null);
        }
        
        // Fetch and set company details
        if (application.getOrganisationId() != null) {
            try {
                ApiResponse<?> companyResponse = companyClient.fetchCompanyById(application.getOrganisationId().toString());
                if (companyResponse != null && companyResponse.isStatus() && companyResponse.getData() != null) {
                    Map<String, Object> companyData = (Map<String, Object>) companyResponse.getData();
                    dto.setCompanyName((String) companyData.get("name"));
                    dto.setPhysicalAddress((String) companyData.get("physicalAddress"));
                    dto.setTelephoneNumber((String) companyData.get("telephoneNumber"));
                    dto.setFaxNumber((String) companyData.get("faxNumber"));
                    dto.setEmail((String) companyData.get("email"));
                }
            } catch (Exception e) {
                logger.error("Error fetching company details for application {}: {}", application.getId(), e.getMessage());
            }
        }
        
        return dto;
    }

    /**
     * Creates empty response for when no applications are found
     */
    @Override
    public Map<String, Object> createEmptyResponse(int pageNumber, int size) {
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("currentPage", pageNumber);
        metadata.put("totalPages", 0);
        metadata.put("totalElements", 0L);
        metadata.put("pageSize", size);

        // Add empty status counts with all the new fields
        Map<String, Object> statusCounts = new HashMap<>();
        statusCounts.put("totalApplications", 0L);
        statusCounts.put("pendingVetting", 0L);
        statusCounts.put("vettingOngoing", 0L);
        statusCounts.put("awaitingChanges", 0L);
        statusCounts.put("rejected", 0L);
        statusCounts.put("approved", 0L);
        statusCounts.put("underReview", 0L);
        statusCounts.put("pendingApproval", 0L);

        metadata.put("statusCounts", statusCounts);
        response.put("metadata", metadata);
        response.put("content", new ArrayList<>());
        return response;
    }

    /**
     * Creates metadata for pagination and status counts
     */
    private Map<String, Object> createMetadata(Page<PreApprovalApplication> page, Specification<PreApprovalApplication> baseSpec) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("currentPage", page.getNumber());
        metadata.put("totalPages", page.getTotalPages());
        metadata.put("totalElements", page.getTotalElements());
        metadata.put("pageSize", page.getSize());
        
        // Add status counts using our optimized method that uses a single query
        try {
            Map<String, Long> statusCounts = applicationRepository.getApplicationStatusCounts(baseSpec);
            metadata.put("statusCounts", statusCounts);
        } catch (Exception e) {
            logger.error("Error calculating status counts: {}", e.getMessage());
            
         }
        
        return metadata;
    }

    @Override
    @Transactional
    public BatchStatusUpdateResult batchUpdateApplicationStatus(
            List<UUID> applicationIds,
            String role,
            String action,
            String userId,
            String comments,
            String newAssignee) {
        
        logger.info("Batch status update initiated for {} pre-approval applications", 
                applicationIds != null ? applicationIds.size() : 0);
        
        BatchStatusUpdateResult result = new BatchStatusUpdateResult();
        List<BatchStatusUpdateResult.ApplicationUpdateResult> updateResults = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        
        // Create a new batch record
        Batch batch = new Batch();
        batch.setBatchType("pre_approval");
        batch.setApplicationStatus(ApplicationStatus.valueOf(action));
        batch.setApplicationCount(applicationIds.size());
        batch.setActionTakenBy(role);
        batch.setUserId(UUID.fromString(userId));
        batch.setDatePosted(new Date());
        // Set batch status to PROCESSING
        batch.setBatchStatus(ApplicationStatus.PROCESSING);
        
        // Save the batch to get an ID
        batch = batchRepository.save(batch);
        UUID batchId = batch.getId();
        
        for (UUID applicationId: applicationIds) {
            try {
                Optional<PreApprovalApplication> applicationOpt = getPreApprovalApplicationEntity(applicationId);
                                
                                if (applicationOpt.isPresent()) {
                                    PreApprovalApplication application = applicationOpt.get();
                                    
                                    // Check if application is already in REJECTED status
                                    if ("REJECTED".equals(application.getStatus())) {
                                        updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                                applicationId, false, "Application is already rejected and cannot be updated"));
                                        failureCount++;
                                        continue;
                                    }
                                    
                                    // Check if application is already in CHANGE_REQUEST status
                                    if ("CHANGE_REQUEST".equals(application.getStatus())) {
                                        updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                                applicationId, false, "Application is already in change request status and cannot be approved"));
                                        failureCount++;
                                        continue;
                                    }
                                    
                                    int updateResult = updateApplicationStatus(applicationId, role, action, newAssignee);
                                    
                                    if (updateResult > 0) {
                                       
                                        PreApprovalApplicationComments logEntry = new PreApprovalApplicationComments();
                                        logEntry.setApplication(application);
                                        logEntry.setAction(action);
                                        logEntry.setComments(sanitizeHtml(comments));
                                        logEntry.setUpdatedBy(userId);
                                        logEntry.setTimestamp(LocalDateTime.now());
                                        logEntry.setBatchId(batchId);
                                        
                                        // Save the comment
                                        commentsService.createComments(logEntry);
                                        
                                        updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                                applicationId, true, "Status updated successfully"));
                                        successCount++;
                                    } else {
                                        updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                                applicationId, false, "Failed to update application status"));
                                        failureCount++;
                                    }
                                } else {
                                    updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                            applicationId, false, "Application not found"));
                                    failureCount++;
                                }
                            } catch (Exception e) {
                                updateResults.add(new BatchStatusUpdateResult.ApplicationUpdateResult(
                                        applicationId, false, "Error: " + e.getMessage()));
                                failureCount++;
                            }
                        }
                
                        result.setTotalProcessed(applicationIds.size());
                        result.setSuccessCount(successCount);
                        result.setFailureCount(failureCount);
                        result.setResults(updateResults);
                        
                        // Update the batch with the results
                        ApplicationStatus finalStatus = (failureCount == 0) ? 
                                ApplicationStatus.PROCESSED : ApplicationStatus.PARTIALLY_PROCESSED;
                        batch.setBatchStatus(finalStatus);
                        
                        // Store the full response in batch_logs
                        Map<String, Object> batchLogs = new HashMap<>();
                        batchLogs.put("timestamp", new Date());
                        batchLogs.put("action", action);
                        batchLogs.put("role", role);
                        batchLogs.put("result", result);
                        batch.setBatchLogs(batchLogs);
                        
                        // Update the batch record
                        batchRepository.save(batch);
                        
                        return result;
                    }


	@Override
    public boolean updateProcessInstanceIdToPreApprovalApplication(String preApprovalId, String processInstanceId) {

        PreApprovalApplication application = applicationRepository.findById(UUID.fromString(preApprovalId))
                .orElseThrow(() -> new ApplicationNotFoundException("Application not found with ID: " + preApprovalId));

        application.setProcessInstanceId(processInstanceId);
        applicationRepository.save(application);
        logger.info("Process instance ID {} saved for application {}", processInstanceId, preApprovalId);

        return true;
    }

    @Override
    @Transactional
    public boolean updatePaperlessIdToPreApprovalApplication(String applicationId, String paperlessId) {
        try {
            logger.info("Updating paperlessId {} for application {}", paperlessId, applicationId);

            PreApprovalApplication application = applicationRepository.findById(UUID.fromString(applicationId))
                    .orElseThrow(() -> new ApplicationNotFoundException("Application not found with ID: " + applicationId));

            application.setPaperlessId(paperlessId);
            applicationRepository.save(application);
            logger.info("Paperless ID {} saved for application {}", paperlessId, applicationId);

            return true;
        } catch (Exception e) {
            logger.error("Error updating paperlessId for application {}: {}", applicationId, e.getMessage());
            return false;
        }
    }

    @Override
    @Transactional
    public Map<String, Object> updatePaperlessIdAndSaveDocument(String applicationId, Map<String, String> payload) {
        Map<String, Object> response = new HashMap<>();

        try {
            String paperlessId = payload.get("paperlessId");
            logger.info("Updating paperlessId {} and saving document for pre-approval application {}", paperlessId, applicationId);

            // First, update the pre-approval application with paperless ID (if needed)
            PreApprovalApplication application = applicationRepository.findById(UUID.fromString(applicationId))
                    .orElseThrow(() -> new ApplicationNotFoundException("Pre-Approval Application not found with ID: " + applicationId));

            if (paperlessId != null && !paperlessId.isEmpty()) {
                application.setPaperlessId(paperlessId);
                applicationRepository.save(application);
                logger.info("Paperless ID {} updated for pre-approval application {}", paperlessId, applicationId);
            }

            // Then, save document details to trn_documents table
            DocumentCommon document = new DocumentCommon();
            document.setKey(paperlessId); // paperlessId is the key field
            document.setIdentifier(applicationId); // Link to the pre-approval application

            // Extract other fields from payload if they exist
            String docName = payload.getOrDefault("docName", "Pre-Approval Application Document");
            String docExt = payload.getOrDefault("docExt", "pdf");
            String fileType = payload.getOrDefault("fileType", "application/pdf");
            String fileUrl = payload.getOrDefault("fileUrl", "");

            document.setDocName(docName);
            document.setDocExt(docExt);
            document.setFileType(fileType);
            document.setFileUrl(fileUrl);

            // Handle docSize - convert string to integer if provided
            Integer docSize = null;
            String docSizeStr = payload.get("docSize");
            if (docSizeStr != null && !docSizeStr.isEmpty()) {
                try {
                    docSize = Integer.parseInt(docSizeStr);
                    document.setDocSize(docSize);
                } catch (NumberFormatException e) {
                    logger.warn("Invalid docSize format: {}, setting to null", docSizeStr);
                    document.setDocSize(null);
                }
            }

            // Save the document
            DocumentCommon savedDocument = documentService.saveDocument(document);
            logger.info("Document saved successfully with ID: {} for pre-approval application: {}",
                    savedDocument.getId(), applicationId);

            // Build successful response
            response.put("success", true);
            response.put("message", "Paperless ID updated and document details saved successfully");
            response.put("applicationId", applicationId);
            response.put("paperlessId", paperlessId);
            response.put("documentId", savedDocument.getId().toString());
            response.put("docName", docName);
            response.put("docExt", docExt);
            response.put("fileType", fileType);
            response.put("fileUrl", fileUrl);
            if (docSize != null) {
                response.put("docSize", docSize);
            }
            response.put("createdAt", savedDocument.getCreatedAt());
            response.put("updatedAt", savedDocument.getUpdatedAt());

            return response;

        } catch (ApplicationNotFoundException e) {
            logger.error("Pre-approval application not found: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorType", "PRE_APPROVAL_APPLICATION_NOT_FOUND");
            return response;

        } catch (Exception e) {
            logger.error("Error updating paperlessId and saving document for pre-approval application {}: {}", applicationId, e.getMessage());
            response.put("success", false);
            response.put("message", "Failed to update paperless ID and save document: " + e.getMessage());
            response.put("errorType", "INTERNAL_ERROR");
            return response;
        }
    }

}
