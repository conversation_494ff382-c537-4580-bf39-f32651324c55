2025-06-30T12:03:47.510+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 12076 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-30T12:03:47.562+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-30T12:03:47.881+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-30T12:03:47.895+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-30T12:03:52.034+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-30T12:03:52.909+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 862 ms. Found 31 JPA repository interfaces.
2025-06-30T12:03:53.800+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0a898feb-cb30-3132-b195-babb9ec03f8f
2025-06-30T12:03:55.104+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:03:55.116+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:03:55.134+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:03:55.141+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:03:55.406+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:03:56.762+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-30T12:03:56.807+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-30T12:03:56.808+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-30T12:03:56.936+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-30T12:03:56.936+05:30  INFO 12076 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 9030 ms
2025-06-30T12:03:57.308+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-30T12:03:58.762+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@fdcdf7
2025-06-30T12:03:58.810+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-30T12:03:59.002+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-30T12:04:00.636+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-30T12:04:00.891+05:30  INFO 12076 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-30T12:04:01.004+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-30T12:04:02.977+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-30T12:04:08.985+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-30T12:04:10.264+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:04:11.125+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-30T12:04:13.076+05:30  WARN 12076 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30T12:04:13.169+05:30  WARN 12076 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-30T12:04:13.244+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-30T12:04:13.372+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-30T12:04:17.701+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-30T12:04:19.417+05:30  INFO 12076 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-30T12:04:19.508+05:30  WARN 12076 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-30T12:04:19.657+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-30T12:04:19.696+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-30T12:04:19.707+05:30  INFO 12076 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:04:19.724+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-30T12:04:19.725+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-30T12:04:19.725+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-30T12:04:19.726+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-30T12:04:19.726+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-30T12:04:19.727+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-30T12:04:19.727+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-30T12:04:20.275+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-30T12:04:20.277+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-30T12:04:20.279+05:30  INFO 12076 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-30T12:04:20.284+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751265260281 with initial instances count: 5
2025-06-30T12:04:20.298+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-30T12:04:20.299+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751265260299, current=UP, previous=STARTING]
2025-06-30T12:04:20.301+05:30  WARN 12076 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-30T12:04:20.302+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-30T12:04:20.321+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-30T12:04:20.323+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-30T12:04:20.363+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 34.317 seconds (process running for 36.081)
2025-06-30T12:04:20.368+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-30T12:04:55.776+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-30T12:04:55.778+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-30T12:04:55.797+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 16 ms
2025-06-30T12:04:56.887+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-06-30T12:04:56.897+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-06-30T12:04:56.941+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-06-30T12:04:57.045+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-06-30T12:04:57.508+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-4B59D7
2025-06-30T12:04:57.807+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 1fc3b2dc-ffc2-4835-93c7-a61c120ceb97
2025-06-30T12:04:58.104+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 1fc3b2dc-ffc2-4835-93c7-a61c120ceb97
2025-06-30T12:04:58.785+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-30T12:04:58.824+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-1] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-30T12:05:01.264+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-30T12:05:01.375+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-1] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-30T12:05:01.432+05:30 ERROR 12076 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: WORKFLOW executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/1fc3b2dc-ffc2-4835-93c7-a61c120ceb97
2025-06-30T12:08:51.722+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-06-30T12:08:51.724+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-06-30T12:08:51.725+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-06-30T12:08:51.728+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-06-30T12:08:51.729+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-E33FE6
2025-06-30T12:08:51.732+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: a7de07b2-ed5d-48bc-a60b-ac48bc617a81
2025-06-30T12:08:51.789+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: a7de07b2-ed5d-48bc-a60b-ac48bc617a81
2025-06-30T12:08:51.793+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-2] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-30T12:08:51.793+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-2] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-30T12:08:54.125+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-2] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-30T12:08:54.126+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-2] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-30T12:08:54.127+05:30 ERROR 12076 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: WORKFLOW executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/a7de07b2-ed5d-48bc-a60b-ac48bc617a81
2025-06-30T12:09:19.956+05:30  INFO 12076 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
