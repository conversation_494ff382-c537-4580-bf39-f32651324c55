2025-06-30T12:03:47.510+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 12076 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-30T12:03:47.562+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-30T12:03:47.881+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-30T12:03:47.895+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-30T12:03:52.034+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-30T12:03:52.909+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 862 ms. Found 31 JPA repository interfaces.
2025-06-30T12:03:53.800+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0a898feb-cb30-3132-b195-babb9ec03f8f
2025-06-30T12:03:55.104+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:03:55.116+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:03:55.134+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:03:55.141+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:03:55.406+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:03:56.762+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-30T12:03:56.807+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-30T12:03:56.808+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-30T12:03:56.936+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-30T12:03:56.936+05:30  INFO 12076 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 9030 ms
2025-06-30T12:03:57.308+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-30T12:03:58.762+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@fdcdf7
2025-06-30T12:03:58.810+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-30T12:03:59.002+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-30T12:04:00.636+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-30T12:04:00.891+05:30  INFO 12076 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-30T12:04:01.004+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-30T12:04:02.977+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-30T12:04:08.985+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-30T12:04:10.264+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:04:11.125+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-30T12:04:13.076+05:30  WARN 12076 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30T12:04:13.169+05:30  WARN 12076 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-30T12:04:13.244+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-30T12:04:13.372+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-30T12:04:17.701+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-30T12:04:19.417+05:30  INFO 12076 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-30T12:04:19.508+05:30  WARN 12076 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-30T12:04:19.657+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-30T12:04:19.696+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-30T12:04:19.707+05:30  INFO 12076 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:04:19.724+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-30T12:04:19.725+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-30T12:04:19.725+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-30T12:04:19.726+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-30T12:04:19.726+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-30T12:04:19.727+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-30T12:04:19.727+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-30T12:04:20.275+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-30T12:04:20.277+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-30T12:04:20.279+05:30  INFO 12076 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-30T12:04:20.284+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751265260281 with initial instances count: 5
2025-06-30T12:04:20.298+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-30T12:04:20.299+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751265260299, current=UP, previous=STARTING]
2025-06-30T12:04:20.301+05:30  WARN 12076 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-30T12:04:20.302+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-30T12:04:20.321+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-30T12:04:20.323+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-30T12:04:20.363+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 34.317 seconds (process running for 36.081)
2025-06-30T12:04:20.368+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-30T12:04:55.776+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-30T12:04:55.778+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-30T12:04:55.797+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 16 ms
2025-06-30T12:04:56.887+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-06-30T12:04:56.897+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-06-30T12:04:56.941+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-06-30T12:04:57.045+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-06-30T12:04:57.508+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-4B59D7
2025-06-30T12:04:57.807+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 1fc3b2dc-ffc2-4835-93c7-a61c120ceb97
2025-06-30T12:04:58.104+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 1fc3b2dc-ffc2-4835-93c7-a61c120ceb97
2025-06-30T12:04:58.785+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-30T12:04:58.824+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-1] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-30T12:05:01.264+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-30T12:05:01.375+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-1] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-30T12:05:01.432+05:30 ERROR 12076 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: WORKFLOW executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/1fc3b2dc-ffc2-4835-93c7-a61c120ceb97
2025-06-30T12:08:51.722+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-06-30T12:08:51.724+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-06-30T12:08:51.725+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-06-30T12:08:51.728+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-06-30T12:08:51.729+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-E33FE6
2025-06-30T12:08:51.732+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: a7de07b2-ed5d-48bc-a60b-ac48bc617a81
2025-06-30T12:08:51.789+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: a7de07b2-ed5d-48bc-a60b-ac48bc617a81
2025-06-30T12:08:51.793+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-2] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-30T12:08:51.793+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-2] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-30T12:08:54.125+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-2] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-30T12:08:54.126+05:30  WARN 12076 --- [workplace-learning] [http-nio-8091-exec-2] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-30T12:08:54.127+05:30 ERROR 12076 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Failed to start workflow process: WORKFLOW executing GET http://WORKFLOW/api/v1/workflow/start-process/PRE_APPROVAL/a7de07b2-ed5d-48bc-a60b-ac48bc617a81
2025-06-30T12:09:19.956+05:30  INFO 12076 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:14:20.065+05:30  INFO 12076 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:18:08.097+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-06-30T12:18:08.098+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-06-30T12:18:08.099+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-06-30T12:18:08.102+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-06-30T12:18:08.104+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-19A63D
2025-06-30T12:18:08.108+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 6040a20c-e993-4121-8ece-7c18232b7f93
2025-06-30T12:18:08.181+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 6040a20c-e993-4121-8ece-7c18232b7f93
2025-06-30T12:18:08.886+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-5] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 6040a20c-e993-4121-8ece-7c18232b7f93
2025-06-30T12:18:14.724+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-06-30T12:18:14.725+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-06-30T12:18:14.726+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-06-30T12:18:14.729+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-06-30T12:18:14.730+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-D3AFE1
2025-06-30T12:18:14.734+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:18:14.759+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:18:14.786+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-7] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:18:14.901+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Workflow response: {success=true, applicationData={metadata={applicationNumber=APP-20250630-F09E59, referenceNumber=REF-20250630-EBCF10, state=SUBMITTED, lastModified=2025-06-30T06:48:08.106+00:00, applicationId=6040a20c-e993-4121-8ece-7c18232b7f93, status=PENDING}, attachments=[], application={id=6040a20c-e993-4121-8ece-7c18232b7f93, userId=4b25a526-fd3a-4449-8e00-8d50a6ea258e, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, company={uuid=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, name=BUAN, type=EDUCATION_TRAINING_PROVIDER_LEVY_PAYER, industry=Agriculture, category=GENERAL_EDUCATION, physicalAddress=plot 123, Gwest, Gaborone, telephoneNumber=+**********, faxNumber=+**********, pepPipStatus=false, pepPipAssociateStatus=false, contactPerson={id=1743080519658, uuid=4b25a526-fd3a-4449-8e00-8d50a6ea258e, name=Bonolo, position=Secretary, mobileNumber=+26778951365, email=<EMAIL>}, employees=[{uuid=1d662764-788a-440f-a761-a5b02892275f, firstName=Gon, lastName=Dra, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=1500}, {uuid=e2c55990-3316-42fa-905f-467273c88d53, firstName=Ying, lastName=Yang, idNumber=*********, idType=PASSPORT, idVerificationStatus=UNVERIFIED, gender=FEMALE, contactNumber=+26774503261, basicSalary=3400}, {uuid=27654f7d-244a-4ccd-84c7-df237021e088, firstName=Tha, lastName=Biso, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=150}], registrationApplication={uuid=c206e9cb-6485-4c40-9856-5461c7bc08c2, referenceNumber=APP-20250327-5032E6, status=APPROVED, state=SUBMITTED, applicationSubmissionDate=2025-03-27T13:01:59.663446, actionsRequested=[{uuid=38b6130b-9772-40cb-b87a-7fbb774f40b4, actionType=VERIFY_CIPA_NUMBER, actionMessage=Company CIPA Number verification pending, actionStatus=PENDING}], agentLead=null, agent=unassigned, manager=null}, verifications=[{uuid=9c9616c3-48ae-4de2-9089-3552bf4f93cf, reference=BW86165236462, type=COMPANY_NUMBER, status=UNVERIFIED, verifiedAt=null, expiryDate=null}]}, status=PENDING, state=SUBMITTED, applicationNumber=APP-20250630-F09E59, referenceNumber=REF-20250630-EBCF10, reasonForTraining=employees., courseTitle=Data, trainingProvider=Institute, cityOfTraining=Gaborone, trainingStartDate=2024-01-01, trainingEndDate=2024-03-07, particularOfTrainings=[{id=a1f4928a-9259-4325-838c-a7aa15a3ca00, productRelatedTraining=true, outsourcedTraining=false, nonCitizenEmployee=true, isTrainingLocal=true}], moduleDetails=[{id=ba7feca8-7e79-4594-a89a-8ebfdd1a6f89, moduleName=Data Analysis1, expectedCompetencies=Ability to analyze large datasets, moduleReferenceId=null}, {id=9f4e748b-bc96-4db8-8b5a-96a2a9510cef, moduleName=Machine Learning1, expectedCompetencies=Ability to build predictive models, moduleReferenceId=null}], estimatedTrainingCosts=[{id=be6210f0-2fc2-4adc-b575-b61e0020f480, itemDescription=Course fees, amount=5000.0}, {id=ce47887f-a922-4244-8018-2edbd4845257, itemDescription=Travel expenses, amount=1000.0}], subtotal=6000.0, total=6839.999999999999, vatNumber=BW-19A63D, accreditationEvidence=Certificate from ABC Training Institute, employeeIds=[0f97931c-a8b8-4fa5-9fe7-d7398f4a479b, 709069f7-2ae4-4578-b562-030056aa97b1], assignedAgent=null, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, createdDate=2025-06-30T06:48:08.106+00:00, lastModifiedDate=2025-06-30T06:48:08.106+00:00, createdBy=null, lastModifiedBy=null, totalLearningHours=null, paperlessId=null, attachments=null}}, processInstanceId=2eb920ad-557e-11f0-aaf2-00155d4133f2, message=Process started and application data retrieved successfully}
2025-06-30T12:18:14.955+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Process instance ID 2eb920ad-557e-11f0-aaf2-00155d4133f2 saved for application 6040a20c-e993-4121-8ece-7c18232b7f93
2025-06-30T12:18:14.957+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Process instance ID 2eb920ad-557e-11f0-aaf2-00155d4133f2 saved for application 6040a20c-e993-4121-8ece-7c18232b7f93
2025-06-30T12:18:15.634+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : Workflow response: {success=true, applicationData={metadata={applicationNumber=APP-20250630-63FCB2, referenceNumber=REF-20250630-C83F2E, state=SUBMITTED, lastModified=2025-06-30T06:48:14.733+00:00, applicationId=6042e41e-f9b1-4bc3-8f63-38b3d013b67e, status=PENDING}, attachments=[], application={id=6042e41e-f9b1-4bc3-8f63-38b3d013b67e, userId=4b25a526-fd3a-4449-8e00-8d50a6ea258e, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, company={uuid=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, name=BUAN, type=EDUCATION_TRAINING_PROVIDER_LEVY_PAYER, industry=Agriculture, category=GENERAL_EDUCATION, physicalAddress=plot 123, Gwest, Gaborone, telephoneNumber=+**********, faxNumber=+**********, pepPipStatus=false, pepPipAssociateStatus=false, contactPerson={id=1743080519658, uuid=4b25a526-fd3a-4449-8e00-8d50a6ea258e, name=Bonolo, position=Secretary, mobileNumber=+26778951365, email=<EMAIL>}, employees=[{uuid=1d662764-788a-440f-a761-a5b02892275f, firstName=Gon, lastName=Dra, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=1500}, {uuid=e2c55990-3316-42fa-905f-467273c88d53, firstName=Ying, lastName=Yang, idNumber=*********, idType=PASSPORT, idVerificationStatus=UNVERIFIED, gender=FEMALE, contactNumber=+26774503261, basicSalary=3400}, {uuid=27654f7d-244a-4ccd-84c7-df237021e088, firstName=Tha, lastName=Biso, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=150}], registrationApplication={uuid=c206e9cb-6485-4c40-9856-5461c7bc08c2, referenceNumber=APP-20250327-5032E6, status=APPROVED, state=SUBMITTED, applicationSubmissionDate=2025-03-27T13:01:59.663446, actionsRequested=[{uuid=38b6130b-9772-40cb-b87a-7fbb774f40b4, actionType=VERIFY_CIPA_NUMBER, actionMessage=Company CIPA Number verification pending, actionStatus=PENDING}], agentLead=null, agent=unassigned, manager=null}, verifications=[{uuid=9c9616c3-48ae-4de2-9089-3552bf4f93cf, reference=BW86165236462, type=COMPANY_NUMBER, status=UNVERIFIED, verifiedAt=null, expiryDate=null}]}, status=PENDING, state=SUBMITTED, applicationNumber=APP-20250630-63FCB2, referenceNumber=REF-20250630-C83F2E, reasonForTraining=employees., courseTitle=Data, trainingProvider=Institute, cityOfTraining=Gaborone, trainingStartDate=2024-01-01, trainingEndDate=2024-03-07, particularOfTrainings=[{id=ac9b02de-c15b-4088-8f07-b642efabf7d4, productRelatedTraining=true, outsourcedTraining=false, nonCitizenEmployee=true, isTrainingLocal=true}], moduleDetails=[{id=71c10a98-258b-486d-bf4e-1d086291aa60, moduleName=Data Analysis1, expectedCompetencies=Ability to analyze large datasets, moduleReferenceId=null}, {id=5a75c620-1bd2-49a2-9823-7fbae3d6e9d8, moduleName=Machine Learning1, expectedCompetencies=Ability to build predictive models, moduleReferenceId=null}], estimatedTrainingCosts=[{id=de257841-6d65-4886-9b53-de734d02106e, itemDescription=Course fees, amount=5000.0}, {id=3c4418a6-b0c0-4492-b984-a862bdb8e0e9, itemDescription=Travel expenses, amount=1000.0}], subtotal=6000.0, total=6839.999999999999, vatNumber=BW-D3AFE1, accreditationEvidence=Certificate from ABC Training Institute, employeeIds=[0f97931c-a8b8-4fa5-9fe7-d7398f4a479b, 709069f7-2ae4-4578-b562-030056aa97b1], assignedAgent=null, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, createdDate=2025-06-30T06:48:14.733+00:00, lastModifiedDate=2025-06-30T06:48:14.733+00:00, createdBy=null, lastModifiedBy=null, totalLearningHours=null, paperlessId=null, attachments=null}}, processInstanceId=325dbec4-557e-11f0-aaf2-00155d4133f2, message=Process started and application data retrieved successfully}
2025-06-30T12:18:15.684+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Process instance ID 325dbec4-557e-11f0-aaf2-00155d4133f2 saved for application 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:18:15.694+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : Process instance ID 325dbec4-557e-11f0-aaf2-00155d4133f2 saved for application 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:19:20.079+05:30  INFO 12076 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:20:20.304+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-10] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:20:45.500+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Application user assignment initiated for application ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:20:45.565+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.service.NotificationService    : Sending IN_APP notification to user with ID: 9dbc9c30-d927-47c3-b826-d46ddc42e2cd
2025-06-30T12:20:47.443+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.a.k.clients.producer.ProducerConfig    : Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-06-30T12:20:47.446+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.a.k.clients.producer.ProducerConfig    : ProducerConfig values: 
	acks = 1
	auto.include.jmx.reporter = true
	batch.size = 16384
	bootstrap.servers = [localhost:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = workplace-learning-producer-1
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	enable.metrics.push = true
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 0
	max.block.ms = 10000
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 3
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 1000
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.springframework.kafka.support.serializer.JsonSerializer

2025-06-30T12:20:47.576+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.a.k.c.t.i.KafkaMetricsCollector        : initializing Kafka metrics collector
2025-06-30T12:20:47.741+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.a.kafka.common.utils.AppInfoParser     : Kafka version: 3.7.1
2025-06-30T12:20:47.743+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.a.kafka.common.utils.AppInfoParser     : Kafka commitId: e2494e6ffb89f828
2025-06-30T12:20:47.744+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] o.a.kafka.common.utils.AppInfoParser     : Kafka startTimeMs: 1751266247735
2025-06-30T12:20:48.842+05:30  INFO 12076 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] org.apache.kafka.clients.Metadata        : [Producer clientId=workplace-learning-producer-1] Cluster ID: Hip1S2vfTLmBy_5eol5HHA
2025-06-30T12:20:48.882+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.service.NotificationService    : Successfully sent IN_APP notification <NAME_EMAIL>
2025-06-30T12:20:48.922+05:30  INFO 12076 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] b.o.h.w.w.service.NotificationService    : Sent IN_APP notification <NAME_EMAIL> with offset=[151]
2025-06-30T12:20:58.413+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Application status update initiated for application ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:20:58.420+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Updating status for application ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e, role: AGENT, action: APPROVED, newAssignee: null
2025-06-30T12:20:58.428+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : User Role : AGENT and ref number :6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:20:58.551+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:21:25.485+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-5] h.w.w.c.PreApprovalApplicationController : Application user assignment initiated for application ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:21:25.506+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.service.NotificationService    : Sending IN_APP notification to user with ID: 91c062fe-ae81-459b-bd16-c5cc21d408f8
2025-06-30T12:21:26.818+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.service.NotificationService    : Successfully sent IN_APP notification <NAME_EMAIL>
2025-06-30T12:21:26.820+05:30  INFO 12076 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] b.o.h.w.w.service.NotificationService    : Sent IN_APP notification <NAME_EMAIL> with offset=[158]
2025-06-30T12:21:35.756+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-7] h.w.w.c.PreApprovalApplicationController : Application status update initiated for application ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:21:35.762+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-7] .w.s.i.PreApprovalApplicationServiceImpl : Updating status for application ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e, role: OFFICER, action: APPROVED, newAssignee: null
2025-06-30T12:21:35.770+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-7] h.w.w.c.PreApprovalApplicationController : User Role : OFFICER and ref number :6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:21:35.833+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:21:50.139+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : Application status update initiated for application ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:21:50.143+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Updating status for application ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e, role: MANAGER, action: APPROVED, newAssignee: null
2025-06-30T12:21:50.149+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-6] h.w.w.c.PreApprovalApplicationController : User Role : MANAGER and ref number :6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:21:50.184+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-8] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:21:55.949+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-9] h.w.w.c.PreApprovalApplicationController : Updating paperlessId for application ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:21:55.949+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-9] .w.s.i.PreApprovalApplicationServiceImpl : Updating paperlessId 165 for application 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:21:55.954+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-9] .w.s.i.PreApprovalApplicationServiceImpl : Paperless ID 165 saved for application 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:22:13.671+05:30  INFO 12076 --- [workplace-learning] [http-nio-8091-exec-10] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 6042e41e-f9b1-4bc3-8f63-38b3d013b67e
2025-06-30T12:24:20.085+05:30  INFO 12076 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:29:20.097+05:30  INFO 12076 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:29:48.976+05:30  INFO 12076 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] org.apache.kafka.clients.NetworkClient   : [Producer clientId=workplace-learning-producer-1] Node -1 disconnected.
2025-06-30T12:34:20.107+05:30  INFO 12076 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:39:20.112+05:30  INFO 12076 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:39:48.915+05:30  INFO 12076 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] org.apache.kafka.clients.NetworkClient   : [Producer clientId=workplace-learning-producer-1] Node -1 disconnected.
2025-06-30T12:44:20.117+05:30  INFO 12076 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:47:45.851+05:30  INFO 12076 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-30T12:47:46.044+05:30  INFO 12076 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-30T12:47:46.092+05:30  INFO 12076 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751267866092, current=DOWN, previous=UP]
2025-06-30T12:47:46.195+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751267866195, current=UP, previous=DOWN]
2025-06-30T12:47:46.252+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-30T12:47:46.340+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-30T12:47:46.362+05:30  INFO 12076 --- [workplace-learning] [Thread-1] o.a.k.clients.producer.KafkaProducer     : [Producer clientId=workplace-learning-producer-1] Closing the Kafka producer with timeoutMillis = 30000 ms.
2025-06-30T12:47:46.487+05:30  INFO 12076 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Metrics scheduler closed
2025-06-30T12:47:46.579+05:30  INFO 12076 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-30T12:47:46.658+05:30  INFO 12076 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-06-30T12:47:46.728+05:30  INFO 12076 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Metrics reporters closed
2025-06-30T12:47:46.797+05:30  INFO 12076 --- [workplace-learning] [Thread-1] o.a.kafka.common.utils.AppInfoParser     : App info kafka.producer for workplace-learning-producer-1 unregistered
2025-06-30T12:47:47.009+05:30  INFO 12076 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:47:47.043+05:30  INFO 12076 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-30T12:47:47.124+05:30  INFO 12076 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-30T12:47:47.132+05:30  INFO 12076 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-30T12:47:50.241+05:30  INFO 12076 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-30T12:47:50.494+05:30  INFO 12076 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-30T12:47:50.569+05:30  INFO 12076 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-30T12:47:51.322+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-30T12:47:52.520+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 12076 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-30T12:47:52.601+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-30T12:48:02.875+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-30T12:48:04.340+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1394 ms. Found 31 JPA repository interfaces.
2025-06-30T12:48:07.291+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0a898feb-cb30-3132-b195-babb9ec03f8f
2025-06-30T12:48:08.710+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:48:08.748+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:48:08.796+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:48:08.830+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:48:08.864+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:48:09.975+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-30T12:48:09.985+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-30T12:48:10.008+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-30T12:48:10.148+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-30T12:48:10.150+05:30  INFO 12076 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 17453 ms
2025-06-30T12:48:10.628+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-30T12:48:10.810+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@1f384aa9
2025-06-30T12:48:10.812+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-30T12:48:10.813+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-30T12:48:11.377+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-30T12:48:11.390+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-30T12:48:11.467+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-30T12:48:15.705+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-30T12:48:17.141+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:48:18.911+05:30  WARN 12076 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30T12:48:19.230+05:30  WARN 12076 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-30T12:48:19.443+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-30T12:48:19.574+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-30T12:48:26.454+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-30T12:48:29.911+05:30  INFO 12076 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-30T12:48:30.171+05:30  WARN 12076 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-30T12:48:30.346+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-30T12:48:30.365+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-30T12:48:30.371+05:30  INFO 12076 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:48:30.379+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-30T12:48:30.382+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-30T12:48:30.384+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-30T12:48:30.386+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-30T12:48:30.387+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-30T12:48:30.388+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-30T12:48:30.390+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-30T12:48:30.572+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-30T12:48:30.573+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-30T12:48:30.575+05:30  INFO 12076 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-30T12:48:30.576+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751267910576 with initial instances count: 6
2025-06-30T12:48:30.607+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-30T12:48:30.629+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751267910629, current=UP, previous=STARTING]
2025-06-30T12:48:30.643+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-30T12:48:30.643+05:30  WARN 12076 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-30T12:48:30.693+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-30T12:48:30.803+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-30T12:48:30.809+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-30T12:48:30.915+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 39.495 seconds (process running for 2686.633)
2025-06-30T12:48:31.019+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-30T12:48:31.987+05:30  INFO 12076 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-30T12:48:31.991+05:30  INFO 12076 --- [workplace-learning] [Thread-7] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-30T12:48:31.996+05:30  INFO 12076 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751267911996, current=DOWN, previous=UP]
2025-06-30T12:48:31.997+05:30  WARN 12076 --- [workplace-learning] [Thread-7] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-30T12:48:32.032+05:30  INFO 12076 --- [workplace-learning] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:48:32.036+05:30  INFO 12076 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-30T12:48:32.048+05:30  INFO 12076 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-30T12:48:32.050+05:30  INFO 12076 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-30T12:48:35.059+05:30  INFO 12076 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-30T12:48:35.099+05:30  INFO 12076 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-30T12:48:35.103+05:30  INFO 12076 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-30T12:48:35.303+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-30T12:48:35.560+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 12076 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-30T12:48:35.571+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-30T12:48:38.996+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-30T12:48:39.663+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 658 ms. Found 31 JPA repository interfaces.
2025-06-30T12:48:40.264+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0a898feb-cb30-3132-b195-babb9ec03f8f
2025-06-30T12:48:40.527+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:48:40.536+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:48:40.548+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:48:40.557+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:48:40.562+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:48:40.967+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-30T12:48:40.969+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-30T12:48:40.970+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-30T12:48:41.103+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-30T12:48:41.111+05:30  INFO 12076 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5510 ms
2025-06-30T12:48:41.442+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-30T12:48:41.618+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@28ad97b9
2025-06-30T12:48:41.625+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-30T12:48:41.630+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-30T12:48:42.170+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-30T12:48:42.182+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-30T12:48:42.218+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-30T12:48:44.645+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-30T12:48:45.939+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:48:48.390+05:30  WARN 12076 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30T12:48:48.570+05:30  WARN 12076 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-30T12:48:48.714+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-30T12:48:48.973+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-30T12:48:55.570+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-30T12:48:57.897+05:30  INFO 12076 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-30T12:48:58.050+05:30  WARN 12076 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-30T12:48:58.159+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-30T12:48:58.167+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-30T12:48:58.168+05:30  INFO 12076 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:48:58.169+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-30T12:48:58.169+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-30T12:48:58.170+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-30T12:48:58.171+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-30T12:48:58.172+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-30T12:48:58.173+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-30T12:48:58.173+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-30T12:48:58.282+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-30T12:48:58.283+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-30T12:48:58.284+05:30  INFO 12076 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-30T12:48:58.286+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751267938286 with initial instances count: 7
2025-06-30T12:48:58.292+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751267938292, current=UP, previous=STARTING]
2025-06-30T12:48:58.293+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-30T12:48:58.322+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-30T12:48:58.329+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-30T12:48:58.331+05:30  WARN 12076 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-30T12:48:58.342+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-30T12:48:58.346+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-30T12:48:58.401+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 23.079 seconds (process running for 2714.119)
2025-06-30T12:48:58.411+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-30T12:48:59.724+05:30  INFO 12076 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-30T12:48:59.725+05:30  INFO 12076 --- [workplace-learning] [Thread-13] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-30T12:48:59.726+05:30  INFO 12076 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751267939726, current=DOWN, previous=UP]
2025-06-30T12:48:59.727+05:30  WARN 12076 --- [workplace-learning] [Thread-13] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-30T12:48:59.748+05:30  INFO 12076 --- [workplace-learning] [Thread-13] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:48:59.750+05:30  INFO 12076 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-30T12:48:59.754+05:30  INFO 12076 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-30T12:48:59.757+05:30  INFO 12076 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-30T12:49:02.772+05:30  INFO 12076 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-30T12:49:02.790+05:30  INFO 12076 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-30T12:49:02.791+05:30  INFO 12076 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-30T12:49:02.914+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-30T12:49:03.013+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 12076 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-30T12:49:03.014+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-30T12:49:04.059+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-30T12:49:04.249+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 189 ms. Found 31 JPA repository interfaces.
2025-06-30T12:49:04.665+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0a898feb-cb30-3132-b195-babb9ec03f8f
2025-06-30T12:49:04.799+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:49:04.803+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:49:04.809+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:49:04.815+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:49:04.819+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:49:05.116+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-30T12:49:05.124+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-30T12:49:05.125+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-30T12:49:05.246+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-30T12:49:05.247+05:30  INFO 12076 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2230 ms
2025-06-30T12:49:05.664+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-30T12:49:05.813+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@7d965341
2025-06-30T12:49:05.814+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-30T12:49:05.814+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-30T12:49:06.076+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-30T12:49:06.085+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-30T12:49:06.108+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-30T12:49:09.081+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-30T12:49:10.726+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:49:12.705+05:30  WARN 12076 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30T12:49:12.877+05:30  WARN 12076 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-30T12:49:13.034+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-30T12:49:13.258+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-30T12:49:18.573+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-30T12:49:21.112+05:30  INFO 12076 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-30T12:49:21.214+05:30  WARN 12076 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-30T12:49:21.350+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-30T12:49:21.361+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-30T12:49:21.362+05:30  INFO 12076 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-30T12:49:21.363+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-30T12:49:21.363+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-30T12:49:21.365+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-30T12:49:21.365+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-30T12:49:21.366+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-30T12:49:21.366+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-30T12:49:21.368+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-30T12:49:21.450+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-30T12:49:21.454+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-30T12:49:21.455+05:30  INFO 12076 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-30T12:49:21.459+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751267961459 with initial instances count: 7
2025-06-30T12:49:21.466+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751267961466, current=UP, previous=STARTING]
2025-06-30T12:49:21.467+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-30T12:49:21.485+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-30T12:49:21.509+05:30  WARN 12076 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-30T12:49:21.513+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-30T12:49:21.545+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-30T12:49:21.566+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-30T12:49:21.702+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 18.782 seconds (process running for 2737.42)
2025-06-30T12:49:21.733+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-30T12:49:23.334+05:30  INFO 12076 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-06-30T12:49:23.762+05:30  INFO 12076 --- [workplace-learning] [Thread-19] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-30T12:49:23.851+05:30  INFO 12076 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751267963851, current=DOWN, previous=UP]
2025-06-30T12:49:23.928+05:30  WARN 12076 --- [workplace-learning] [Thread-19] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-30T12:49:24.009+05:30  INFO 12076 --- [workplace-learning] [Thread-19] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:49:24.056+05:30  INFO 12076 --- [workplace-learning] [Thread-19] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-30T12:49:24.192+05:30  INFO 12076 --- [workplace-learning] [Thread-19] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-30T12:49:24.211+05:30  INFO 12076 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-30T12:49:27.334+05:30  INFO 12076 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-30T12:49:27.334+05:30  WARN 12076 --- [workplace-learning] [DiscoveryClient-%d] c.netflix.discovery.TimedSupervisorTask  : task supervisor shutting down, can't accept the task
2025-06-30T12:49:27.708+05:30  INFO 12076 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-30T12:49:28.141+05:30  INFO 12076 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-30T12:49:29.554+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - Re-registering apps/WORKPLACE-LEARNING
2025-06-30T12:49:29.575+05:30  INFO 12076 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-30T12:49:29.572+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-30T12:49:29.812+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 12076 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-30T12:49:29.860+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-30T12:49:33.356+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-30T12:49:33.821+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 462 ms. Found 31 JPA repository interfaces.
2025-06-30T12:49:34.406+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0134045a-a09b-303f-a25e-e1d24242c1fe
2025-06-30T12:49:34.709+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:49:34.717+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:49:34.732+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:49:34.742+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:49:34.753+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:49:35.328+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-30T12:49:35.330+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-30T12:49:35.331+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-30T12:49:35.486+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-30T12:49:35.489+05:30  INFO 12076 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5537 ms
2025-06-30T12:49:36.161+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-30T12:49:36.484+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@276d4f46
2025-06-30T12:49:36.485+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-30T12:49:36.489+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-30T12:49:37.167+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-30T12:49:37.181+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-30T12:49:37.219+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-30T12:49:39.944+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-30T12:49:41.227+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:49:41.507+05:30  WARN 12076 --- [workplace-learning] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'applicationDocumentController': Resolution of declared constructors on bean Class [bw.org.hrdc.weblogic.workplacelearning.controller.ApplicationDocumentController] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@5ab7ae99] failed
2025-06-30T12:49:41.517+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:49:41.521+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-30T12:49:41.536+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-30T12:49:41.539+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-30T12:49:41.619+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-30T12:49:41.803+05:30 ERROR 12076 --- [workplace-learning] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'applicationDocumentController': Resolution of declared constructors on bean Class [bw.org.hrdc.weblogic.workplacelearning.controller.ApplicationDocumentController] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@5ab7ae99] failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:384) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1314) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1209) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352) ~[spring-boot-3.3.5.jar:3.3.5]
	at bw.org.hrdc.weblogic.workplacelearning.WorkplaceLearningApplication.main(WorkplaceLearningApplication.java:28) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.3.5.jar:3.3.5]
Caused by: java.lang.NoClassDefFoundError: GeneratedDocumentService
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3549) ~[na:na]
	at java.base/java.lang.Class.getDeclaredConstructors(Class.java:2727) ~[na:na]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:379) ~[spring-beans-6.1.14.jar:6.1.14]
	... 21 common frames omitted
Caused by: java.lang.ClassNotFoundException: GeneratedDocumentService
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	at java.base/java.lang.Class.forName0(Native Method) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:534) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:513) ~[na:na]
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:121) ~[spring-boot-devtools-3.3.5.jar:3.3.5]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	... 25 common frames omitted

2025-06-30T12:49:44.644+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-30T12:49:45.473+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 12076 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-30T12:49:45.563+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-30T12:49:54.596+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-30T12:49:59.356+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4637 ms. Found 31 JPA repository interfaces.
2025-06-30T12:50:00.416+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=53615ba4-c26a-3322-aa03-4ab205f8cad7
2025-06-30T12:50:01.023+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:50:01.250+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:50:01.371+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:50:01.415+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:50:01.479+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:50:02.500+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-30T12:50:02.565+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-30T12:50:02.636+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-30T12:50:02.865+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat-1].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-06-30T12:50:02.926+05:30  INFO 12076 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 17195 ms
2025-06-30T12:50:03.613+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-30T12:50:03.910+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@6a3b46be
2025-06-30T12:50:03.925+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-30T12:50:03.946+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-30T12:50:04.967+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-30T12:50:05.063+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-30T12:50:05.167+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-30T12:50:08.217+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-30T12:50:09.768+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:50:10.173+05:30  WARN 12076 --- [workplace-learning] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'applicationDocumentController' defined in file [C:\project\ehrdcc-project\workplace-learning\target\classes\bw\org\hrdc\weblogic\workplacelearning\controller\ApplicationDocumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'generatedDocumentService': Resolution of declared constructors on bean Class [bw.org.hrdc.weblogic.workplacelearning.service.document.GeneratedDocumentService] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@4730e808] failed
2025-06-30T12:50:10.206+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:50:10.234+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-30T12:50:10.265+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-30T12:50:10.269+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-30T12:50:10.303+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-30T12:50:10.313+05:30 ERROR 12076 --- [workplace-learning] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'applicationDocumentController' defined in file [C:\project\ehrdcc-project\workplace-learning\target\classes\bw\org\hrdc\weblogic\workplacelearning\controller\ApplicationDocumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'generatedDocumentService': Resolution of declared constructors on bean Class [bw.org.hrdc.weblogic.workplacelearning.service.document.GeneratedDocumentService] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@4730e808] failed
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352) ~[spring-boot-3.3.5.jar:3.3.5]
	at bw.org.hrdc.weblogic.workplacelearning.WorkplaceLearningApplication.main(WorkplaceLearningApplication.java:28) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.3.5.jar:3.3.5]
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'generatedDocumentService': Resolution of declared constructors on bean Class [bw.org.hrdc.weblogic.workplacelearning.service.document.GeneratedDocumentService] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@4730e808] failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:384) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1314) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1209) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782) ~[spring-beans-6.1.14.jar:6.1.14]
	... 22 common frames omitted
Caused by: java.lang.NoClassDefFoundError: FileDocumentMapper
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3549) ~[na:na]
	at java.base/java.lang.Class.getDeclaredConstructors(Class.java:2727) ~[na:na]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:379) ~[spring-beans-6.1.14.jar:6.1.14]
	... 35 common frames omitted
Caused by: java.lang.ClassNotFoundException: FileDocumentMapper
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	at java.base/java.lang.Class.forName0(Native Method) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:534) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:513) ~[na:na]
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:121) ~[spring-boot-devtools-3.3.5.jar:3.3.5]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	... 39 common frames omitted

2025-06-30T12:50:40.090+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-30T12:50:40.380+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 12076 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-30T12:50:40.383+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-30T12:50:43.335+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-30T12:50:43.625+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 288 ms. Found 31 JPA repository interfaces.
2025-06-30T12:50:44.078+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=53615ba4-c26a-3322-aa03-4ab205f8cad7
2025-06-30T12:50:44.345+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:50:44.356+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:50:44.379+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-30T12:50:44.389+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:50:44.398+05:30  WARN 12076 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-30T12:50:44.974+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-30T12:50:44.978+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-30T12:50:44.979+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-30T12:50:45.210+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat-2].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-06-30T12:50:45.212+05:30  INFO 12076 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4816 ms
2025-06-30T12:50:45.709+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-30T12:50:45.933+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@2623e653
2025-06-30T12:50:45.950+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-30T12:50:45.952+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-30T12:50:46.458+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-30T12:50:46.463+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-30T12:50:46.491+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-30T12:50:48.700+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-30T12:50:50.664+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:50:50.844+05:30  WARN 12076 --- [workplace-learning] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'applicationDocumentController' defined in file [C:\project\ehrdcc-project\workplace-learning\target\classes\bw\org\hrdc\weblogic\workplacelearning\controller\ApplicationDocumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'generatedDocumentService': Resolution of declared constructors on bean Class [bw.org.hrdc.weblogic.workplacelearning.service.document.GeneratedDocumentService] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@53ddb530] failed
2025-06-30T12:50:50.873+05:30  INFO 12076 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-30T12:50:50.881+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-30T12:50:50.926+05:30  INFO 12076 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-30T12:50:50.942+05:30  INFO 12076 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-30T12:50:50.995+05:30  INFO 12076 --- [workplace-learning] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-30T12:50:51.042+05:30 ERROR 12076 --- [workplace-learning] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'applicationDocumentController' defined in file [C:\project\ehrdcc-project\workplace-learning\target\classes\bw\org\hrdc\weblogic\workplacelearning\controller\ApplicationDocumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'generatedDocumentService': Resolution of declared constructors on bean Class [bw.org.hrdc.weblogic.workplacelearning.service.document.GeneratedDocumentService] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@53ddb530] failed
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625) ~[spring-context-6.1.14.jar:6.1.14]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363) ~[spring-boot-3.3.5.jar:3.3.5]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352) ~[spring-boot-3.3.5.jar:3.3.5]
	at bw.org.hrdc.weblogic.workplacelearning.WorkplaceLearningApplication.main(WorkplaceLearningApplication.java:28) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.3.5.jar:3.3.5]
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'generatedDocumentService': Resolution of declared constructors on bean Class [bw.org.hrdc.weblogic.workplacelearning.service.document.GeneratedDocumentService] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@53ddb530] failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:384) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1314) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1209) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904) ~[spring-beans-6.1.14.jar:6.1.14]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782) ~[spring-beans-6.1.14.jar:6.1.14]
	... 22 common frames omitted
Caused by: java.lang.NoClassDefFoundError: FileDocumentMapper
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3549) ~[na:na]
	at java.base/java.lang.Class.getDeclaredConstructors(Class.java:2727) ~[na:na]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:379) ~[spring-beans-6.1.14.jar:6.1.14]
	... 35 common frames omitted
Caused by: java.lang.ClassNotFoundException: FileDocumentMapper
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	at java.base/java.lang.Class.forName0(Native Method) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:534) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:513) ~[na:na]
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:121) ~[spring-boot-devtools-3.3.5.jar:3.3.5]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	... 39 common frames omitted

2025-06-30T12:53:33.681+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-30T12:53:34.111+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 12076 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-30T12:53:34.128+05:30  INFO 12076 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
