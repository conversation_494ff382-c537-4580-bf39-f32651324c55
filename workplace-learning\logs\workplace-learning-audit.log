2025-06-28T13:19:52.754+05:30  INFO 9568 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 9568 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-28T13:19:52.779+05:30  INFO 9568 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-28T13:19:52.994+05:30  INFO 9568 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-28T13:19:52.995+05:30  INFO 9568 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-28T13:19:56.877+05:30  INFO 9568 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-28T13:19:58.191+05:30  INFO 9568 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1288 ms. Found 31 JPA repository interfaces.
2025-06-28T13:19:59.074+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0a898feb-cb30-3132-b195-babb9ec03f8f
2025-06-28T13:20:00.056+05:30  WARN 9568 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-28T13:20:00.067+05:30  WARN 9568 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-28T13:20:00.084+05:30  WARN 9568 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-28T13:20:00.095+05:30  WARN 9568 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-28T13:20:00.104+05:30  WARN 9568 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-28T13:20:01.924+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-28T13:20:01.959+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-28T13:20:01.962+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-28T13:20:02.139+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-28T13:20:02.141+05:30  INFO 9568 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 9142 ms
2025-06-28T13:20:02.762+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-28T13:20:03.272+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@342fc13c
2025-06-28T13:20:03.276+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-28T13:20:03.300+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-28T13:20:04.130+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-28T13:20:04.278+05:30  INFO 9568 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-28T13:20:04.352+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-28T13:20:04.939+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-28T13:20:08.317+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-28T13:20:09.525+05:30  INFO 9568 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-28T13:20:11.135+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-28T13:20:13.331+05:30  WARN 9568 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-28T13:20:13.428+05:30  WARN 9568 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-28T13:20:13.490+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-28T13:20:13.604+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-28T13:20:17.293+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-28T13:20:18.811+05:30  INFO 9568 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-28T13:20:18.899+05:30  WARN 9568 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-28T13:20:19.003+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-28T13:20:19.030+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-28T13:20:19.034+05:30  INFO 9568 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T13:20:19.047+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-28T13:20:19.048+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-28T13:20:19.048+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-28T13:20:19.049+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-28T13:20:19.049+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-28T13:20:19.049+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-28T13:20:19.050+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-28T13:20:19.531+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-28T13:20:19.532+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-28T13:20:19.535+05:30  INFO 9568 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-28T13:20:19.537+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751097019536 with initial instances count: 5
2025-06-28T13:20:19.559+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-28T13:20:19.560+05:30  INFO 9568 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751097019560, current=UP, previous=STARTING]
2025-06-28T13:20:19.560+05:30  INFO 9568 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-28T13:20:19.561+05:30  WARN 9568 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-28T13:20:19.579+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-28T13:20:19.581+05:30  INFO 9568 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-28T13:20:19.609+05:30  INFO 9568 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-28T13:20:19.614+05:30  INFO 9568 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 28.197 seconds (process running for 29.252)
2025-06-28T13:25:19.065+05:30  INFO 9568 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T13:30:19.073+05:30  INFO 9568 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T13:35:19.079+05:30  INFO 9568 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T13:39:16.851+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-28T13:39:16.852+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-28T13:39:16.857+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-06-28T13:39:17.051+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-06-28T13:39:17.052+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-06-28T13:39:17.065+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-06-28T13:39:17.099+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-06-28T13:39:17.101+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-974972
2025-06-28T13:39:17.177+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: aa8df744-182d-4788-9c07-e739abaec150
2025-06-28T13:39:17.312+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: aa8df744-182d-4788-9c07-e739abaec150
2025-06-28T13:39:17.984+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-2] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: aa8df744-182d-4788-9c07-e739abaec150
2025-06-28T13:39:22.092+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Workflow response: {message=Process started and application data retrieved successfully, success=true, applicationData={metadata={applicationNumber=APP-20250628-F6361A, referenceNumber=REF-20250628-80B0B3, state=SUBMITTED, lastModified=2025-06-28T08:09:17.126+00:00, applicationId=aa8df744-182d-4788-9c07-e739abaec150, status=PENDING}, attachments=[], application={id=aa8df744-182d-4788-9c07-e739abaec150, userId=4b25a526-fd3a-4449-8e00-8d50a6ea258e, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, company={uuid=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, name=BUAN, type=EDUCATION_TRAINING_PROVIDER_LEVY_PAYER, industry=Agriculture, category=GENERAL_EDUCATION, physicalAddress=plot 123, Gwest, Gaborone, telephoneNumber=+**********, faxNumber=+**********, pepPipStatus=false, pepPipAssociateStatus=false, contactPerson={id=1743080519658, uuid=4b25a526-fd3a-4449-8e00-8d50a6ea258e, name=Bonolo, position=Secretary, mobileNumber=+26778951365, email=<EMAIL>}, employees=[{uuid=1d662764-788a-440f-a761-a5b02892275f, firstName=Gon, lastName=Dra, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=1500}, {uuid=27654f7d-244a-4ccd-84c7-df237021e088, firstName=Tha, lastName=Biso, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=150}, {uuid=e2c55990-3316-42fa-905f-467273c88d53, firstName=Ying, lastName=Yang, idNumber=*********, idType=PASSPORT, idVerificationStatus=UNVERIFIED, gender=FEMALE, contactNumber=+26774503261, basicSalary=3400}], registrationApplication={uuid=c206e9cb-6485-4c40-9856-5461c7bc08c2, referenceNumber=APP-20250327-5032E6, status=APPROVED, state=SUBMITTED, applicationSubmissionDate=2025-03-27T13:01:59.663446, actionsRequested=[{uuid=38b6130b-9772-40cb-b87a-7fbb774f40b4, actionType=VERIFY_CIPA_NUMBER, actionMessage=Company CIPA Number verification pending, actionStatus=PENDING}], agentLead=null, agent=unassigned, manager=null}, verifications=[{uuid=9c9616c3-48ae-4de2-9089-3552bf4f93cf, reference=BW86165236462, type=COMPANY_NUMBER, status=UNVERIFIED, verifiedAt=null, expiryDate=null}]}, status=PENDING, state=SUBMITTED, applicationNumber=APP-20250628-F6361A, referenceNumber=REF-20250628-80B0B3, reasonForTraining=employees., courseTitle=Data, trainingProvider=Institute, cityOfTraining=Gaborone, trainingStartDate=2024-01-01, trainingEndDate=2024-03-07, particularOfTrainings=[{id=8ab6478f-0d97-4019-b658-5ec6da156783, productRelatedTraining=true, outsourcedTraining=false, nonCitizenEmployee=true, isTrainingLocal=true}], moduleDetails=[{id=cc56fbbb-f9d2-4c05-9de1-153431d918dd, moduleName=Data Analysis1, expectedCompetencies=Ability to analyze large datasets, moduleReferenceId=null}, {id=f04901b9-2b2a-499f-8812-91f4897e8b11, moduleName=Machine Learning1, expectedCompetencies=Ability to build predictive models, moduleReferenceId=null}], estimatedTrainingCosts=[{id=2a69e002-e883-4da6-ba29-e15e5eee07c8, itemDescription=Course fees, amount=5000.0}, {id=6e01a28d-a014-455c-9cad-c6d477e91d14, itemDescription=Travel expenses, amount=1000.0}], subtotal=6000.0, total=6839.999999999999, vatNumber=BW-974972, accreditationEvidence=Certificate from ABC Training Institute, employeeIds=[0f97931c-a8b8-4fa5-9fe7-d7398f4a479b, 709069f7-2ae4-4578-b562-030056aa97b1], assignedAgent=null, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, createdDate=2025-06-28T08:09:17.126+00:00, lastModifiedDate=2025-06-28T08:09:17.126+00:00, createdBy=null, lastModifiedBy=null, totalLearningHours=null, paperlessId=null, attachments=null}}, processInstanceId=3023b7c1-53f7-11f0-b48d-00155d4133f2}
2025-06-28T13:39:22.115+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Process instance ID 3023b7c1-53f7-11f0-b48d-00155d4133f2 saved for application aa8df744-182d-4788-9c07-e739abaec150
2025-06-28T13:39:22.116+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Process instance ID 3023b7c1-53f7-11f0-b48d-00155d4133f2 saved for application aa8df744-182d-4788-9c07-e739abaec150
2025-06-28T13:39:28.669+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-06-28T13:39:28.669+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-06-28T13:39:28.671+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-06-28T13:39:28.673+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-06-28T13:39:28.674+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-59BA57
2025-06-28T13:39:28.676+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:39:28.686+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:39:28.698+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:39:29.363+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : Workflow response: {message=Process started and application data retrieved successfully, success=true, applicationData={metadata={applicationNumber=APP-20250628-FDCE84, referenceNumber=REF-20250628-AAD6C6, state=SUBMITTED, lastModified=2025-06-28T08:09:28.674+00:00, applicationId=91ede71f-6cbc-491d-b782-f687deec43dc, status=PENDING}, attachments=[], application={id=91ede71f-6cbc-491d-b782-f687deec43dc, userId=4b25a526-fd3a-4449-8e00-8d50a6ea258e, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, company={uuid=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, name=BUAN, type=EDUCATION_TRAINING_PROVIDER_LEVY_PAYER, industry=Agriculture, category=GENERAL_EDUCATION, physicalAddress=plot 123, Gwest, Gaborone, telephoneNumber=+**********, faxNumber=+**********, pepPipStatus=false, pepPipAssociateStatus=false, contactPerson={id=1743080519658, uuid=4b25a526-fd3a-4449-8e00-8d50a6ea258e, name=Bonolo, position=Secretary, mobileNumber=+26778951365, email=<EMAIL>}, employees=[{uuid=1d662764-788a-440f-a761-a5b02892275f, firstName=Gon, lastName=Dra, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=1500}, {uuid=27654f7d-244a-4ccd-84c7-df237021e088, firstName=Tha, lastName=Biso, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=150}, {uuid=e2c55990-3316-42fa-905f-467273c88d53, firstName=Ying, lastName=Yang, idNumber=*********, idType=PASSPORT, idVerificationStatus=UNVERIFIED, gender=FEMALE, contactNumber=+26774503261, basicSalary=3400}], registrationApplication={uuid=c206e9cb-6485-4c40-9856-5461c7bc08c2, referenceNumber=APP-20250327-5032E6, status=APPROVED, state=SUBMITTED, applicationSubmissionDate=2025-03-27T13:01:59.663446, actionsRequested=[{uuid=38b6130b-9772-40cb-b87a-7fbb774f40b4, actionType=VERIFY_CIPA_NUMBER, actionMessage=Company CIPA Number verification pending, actionStatus=PENDING}], agentLead=null, agent=unassigned, manager=null}, verifications=[{uuid=9c9616c3-48ae-4de2-9089-3552bf4f93cf, reference=BW86165236462, type=COMPANY_NUMBER, status=UNVERIFIED, verifiedAt=null, expiryDate=null}]}, status=PENDING, state=SUBMITTED, applicationNumber=APP-20250628-FDCE84, referenceNumber=REF-20250628-AAD6C6, reasonForTraining=employees., courseTitle=Data, trainingProvider=Institute, cityOfTraining=Gaborone, trainingStartDate=2024-01-01, trainingEndDate=2024-03-07, particularOfTrainings=[{id=18c2c571-83a7-4e1b-b970-b00370814411, productRelatedTraining=true, outsourcedTraining=false, nonCitizenEmployee=true, isTrainingLocal=true}], moduleDetails=[{id=5e729f34-a4e8-4fa9-b7df-fbe334604c0b, moduleName=Data Analysis1, expectedCompetencies=Ability to analyze large datasets, moduleReferenceId=null}, {id=a9cb428b-829c-40ad-b2d2-ffd927fe9513, moduleName=Machine Learning1, expectedCompetencies=Ability to build predictive models, moduleReferenceId=null}], estimatedTrainingCosts=[{id=2181c5e1-4551-4495-ba60-74d448f88d73, itemDescription=Course fees, amount=5000.0}, {id=03346f05-1f77-4950-8c1c-607ccb900284, itemDescription=Travel expenses, amount=1000.0}], subtotal=6000.0, total=6839.999999999999, vatNumber=BW-59BA57, accreditationEvidence=Certificate from ABC Training Institute, employeeIds=[0f97931c-a8b8-4fa5-9fe7-d7398f4a479b, 709069f7-2ae4-4578-b562-030056aa97b1], assignedAgent=null, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, createdDate=2025-06-28T08:09:28.674+00:00, lastModifiedDate=2025-06-28T08:09:28.674+00:00, createdBy=null, lastModifiedBy=null, totalLearningHours=null, paperlessId=null, attachments=null}}, processInstanceId=369f4398-53f7-11f0-b48d-00155d4133f2}
2025-06-28T13:39:29.407+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Process instance ID 369f4398-53f7-11f0-b48d-00155d4133f2 saved for application 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:39:29.427+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : Process instance ID 369f4398-53f7-11f0-b48d-00155d4133f2 saved for application 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:39:43.578+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-5] h.w.w.c.PreApprovalApplicationController : Updating paperlessId for application ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:39:43.579+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-5] .w.s.i.PreApprovalApplicationServiceImpl : Updating paperlessId 160 for application 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:39:43.583+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-5] .w.s.i.PreApprovalApplicationServiceImpl : Paperless ID 160 saved for application 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:40:19.081+05:30  INFO 9568 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T13:40:29.452+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-7] h.w.w.c.PreApprovalApplicationController : Application user assignment initiated for application ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:40:29.504+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.service.NotificationService    : Sending IN_APP notification to user with ID: 9dbc9c30-d927-47c3-b826-d46ddc42e2cd
2025-06-28T13:40:30.840+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-7] o.a.k.clients.producer.ProducerConfig    : Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-06-28T13:40:30.841+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-7] o.a.k.clients.producer.ProducerConfig    : ProducerConfig values: 
	acks = 1
	auto.include.jmx.reporter = true
	batch.size = 16384
	bootstrap.servers = [localhost:9092]
	buffer.memory = 33554432
	client.dns.lookup = use_all_dns_ips
	client.id = workplace-learning-producer-1
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	enable.metrics.push = true
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 0
	max.block.ms = 10000
	max.in.flight.requests.per.connection = 5
	max.request.size = 1048576
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retries = 3
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 1000
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.springframework.kafka.support.serializer.JsonSerializer

2025-06-28T13:40:30.927+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-7] o.a.k.c.t.i.KafkaMetricsCollector        : initializing Kafka metrics collector
2025-06-28T13:40:31.030+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-7] o.a.kafka.common.utils.AppInfoParser     : Kafka version: 3.7.1
2025-06-28T13:40:31.030+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-7] o.a.kafka.common.utils.AppInfoParser     : Kafka commitId: e2494e6ffb89f828
2025-06-28T13:40:31.031+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-7] o.a.kafka.common.utils.AppInfoParser     : Kafka startTimeMs: 1751098231028
2025-06-28T13:40:31.604+05:30  INFO 9568 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] org.apache.kafka.clients.Metadata        : [Producer clientId=workplace-learning-producer-1] Cluster ID: Hip1S2vfTLmBy_5eol5HHA
2025-06-28T13:40:31.642+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.service.NotificationService    : Successfully sent IN_APP notification <NAME_EMAIL>
2025-06-28T13:40:31.674+05:30  INFO 9568 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] b.o.h.w.w.service.NotificationService    : Sent IN_APP notification <NAME_EMAIL> with offset=[91]
2025-06-28T13:40:42.999+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-8] h.w.w.c.PreApprovalApplicationController : Application status update initiated for application ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:40:43.006+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-8] .w.s.i.PreApprovalApplicationServiceImpl : Updating status for application ID: 91ede71f-6cbc-491d-b782-f687deec43dc, role: AGENT, action: APPROVED, newAssignee: null
2025-06-28T13:40:43.013+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-8] h.w.w.c.PreApprovalApplicationController : User Role : AGENT and ref number :91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:40:43.123+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-9] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:40:56.050+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-2] h.w.w.c.PreApprovalApplicationController : Application user assignment initiated for application ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:40:56.065+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.service.NotificationService    : Sending IN_APP notification to user with ID: 91c062fe-ae81-459b-bd16-c5cc21d408f8
2025-06-28T13:40:57.285+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.service.NotificationService    : Successfully sent IN_APP notification <NAME_EMAIL>
2025-06-28T13:40:57.287+05:30  INFO 9568 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] b.o.h.w.w.service.NotificationService    : Sent IN_APP notification <NAME_EMAIL> with offset=[98]
2025-06-28T13:41:10.661+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : Application status update initiated for application ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:41:10.673+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Updating status for application ID: 91ede71f-6cbc-491d-b782-f687deec43dc, role: OFFICER, action: APPROVED, newAssignee: null
2025-06-28T13:41:10.686+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-1] h.w.w.c.PreApprovalApplicationController : User Role : OFFICER and ref number :91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:41:10.744+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:41:22.896+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : Application status update initiated for application ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:41:22.902+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Updating status for application ID: 91ede71f-6cbc-491d-b782-f687deec43dc, role: MANAGER, action: APPROVED, newAssignee: null
2025-06-28T13:41:22.910+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : User Role : MANAGER and ref number :91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:41:22.940+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-5] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:45:19.096+05:30  INFO 9568 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T13:47:44.342+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-10] .h.w.w.c.WorkPlaceTrainingPlanController : Processing create/update request for workplace training plan, isDraft: false
2025-06-28T13:47:44.344+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-10] w.w.s.i.WorkPlaceTrainingPlanServiceImpl : Processing create/update request for workplace training plan, isDraft: false
2025-06-28T13:47:44.345+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-10] w.w.s.i.WorkPlaceTrainingPlanServiceImpl : Creating submitted workplace training plan
2025-06-28T13:47:44.346+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-10] w.w.s.i.WorkPlaceTrainingPlanServiceImpl : Generated application number: APP-20250628-881AC0 and reference number: REF-20250628-D2F4ED
2025-06-28T13:47:44.402+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-10] w.w.s.i.WorkPlaceTrainingPlanServiceImpl : WorkPlaceTrainingPlan created with ID: 863cac75-f030-44d8-9883-af248ed793e2
2025-06-28T13:47:44.440+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-8] w.w.s.i.WorkPlaceTrainingPlanServiceImpl : Fetching workplace training plan by ID: 863cac75-f030-44d8-9883-af248ed793e2
2025-06-28T13:47:47.536+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-10] .h.w.w.c.WorkPlaceTrainingPlanController : Workflow response: {message=Process started and application data retrieved successfully, success=true, applicationData={metadata={applicationNumber=APP-20250628-881AC0, referenceNumber=REF-20250628-D2F4ED, state=SUBMITTED, lastModified=2025-06-28T13:47:44.398214, applicationId=863cac75-f030-44d8-9883-af248ed793e2, status=PENDING}, attachments=[], application={id=863cac75-f030-44d8-9883-af248ed793e2, userId=5993de6d-193e-4fb5-8dcf-dde95ac8b4a5, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, applicationStatus=PENDING, applicationState=SUBMITTED, applicationNumber=APP-20250628-881AC0, referenceNumber=REF-20250628-D2F4ED, financialYear=2026-2027, location=abcd, contactDate=2025-06-28, submissionDate=2025-06-28, assignedAgent=null, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, deleted=false, deletedDate=null, courseDetails=[{id=8135b92f-ebe0-433c-9a35-3f1a12528f7b, programme=Flower Arrangement, skills=None, trainingStartDate=2025-03-29, trainingEndDate=2025-03-29, institution=Shake Spear, location=sfdzfd, accreditingBody=Test, levelOfTraining=NCQF_LEVEL_2, costOfTraining=234343.0, noncitizens=2, citizens=33, peopleTrained=35, courseId=null, isNotLocalCourse=null, hasPreApproval=null}], company={uuid=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, name=BUAN, type=EDUCATION_TRAINING_PROVIDER_LEVY_PAYER, industry=Agriculture, category=GENERAL_EDUCATION, physicalAddress=plot 123, Gwest, Gaborone, telephoneNumber=+**********, faxNumber=+**********, pepPipStatus=false, pepPipAssociateStatus=false, contactPerson={id=1743080519658, uuid=4b25a526-fd3a-4449-8e00-8d50a6ea258e, name=Bonolo, position=Secretary, mobileNumber=+26778951365, email=<EMAIL>}, employees=[{uuid=1d662764-788a-440f-a761-a5b02892275f, firstName=Gon, lastName=Dra, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=1500}, {uuid=27654f7d-244a-4ccd-84c7-df237021e088, firstName=Tha, lastName=Biso, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=150}, {uuid=e2c55990-3316-42fa-905f-467273c88d53, firstName=Ying, lastName=Yang, idNumber=*********, idType=PASSPORT, idVerificationStatus=UNVERIFIED, gender=FEMALE, contactNumber=+26774503261, basicSalary=3400}], registrationApplication={uuid=c206e9cb-6485-4c40-9856-5461c7bc08c2, referenceNumber=APP-20250327-5032E6, status=APPROVED, state=SUBMITTED, applicationSubmissionDate=2025-03-27T13:01:59.663446, actionsRequested=[{uuid=38b6130b-9772-40cb-b87a-7fbb774f40b4, actionType=VERIFY_CIPA_NUMBER, actionMessage=Company CIPA Number verification pending, actionStatus=PENDING}], agentLead=null, agent=unassigned, manager=null}, verifications=[{uuid=9c9616c3-48ae-4de2-9089-3552bf4f93cf, reference=BW86165236462, type=COMPANY_NUMBER, status=UNVERIFIED, verifiedAt=null, expiryDate=null}]}, createdDate=2025-06-28T13:47:44.398214, lastModifiedDate=2025-06-28T13:47:44.398214, createdBy=null, lastModifiedBy=null, attachments=null}}, processInstanceId=5e1b569d-53f8-11f0-b48d-00155d4133f2}
2025-06-28T13:47:47.563+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-10] w.w.s.i.WorkPlaceTrainingPlanServiceImpl : Updated process instance ID for training plan 863cac75-f030-44d8-9883-af248ed793e2 to 5e1b569d-53f8-11f0-b48d-00155d4133f2
2025-06-28T13:47:47.564+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-10] .h.w.w.c.WorkPlaceTrainingPlanController : Process instance ID 5e1b569d-53f8-11f0-b48d-00155d4133f2 saved for application 863cac75-f030-44d8-9883-af248ed793e2
2025-06-28T13:47:47.567+05:30  INFO 9568 --- [workplace-learning] [http-nio-8091-exec-10] w.w.s.i.WorkPlaceTrainingPlanServiceImpl : Fetching pre-approval application by reference number: REF-20250628-D2F4ED
2025-06-28T13:49:31.854+05:30  INFO 9568 --- [workplace-learning] [kafka-producer-network-thread | workplace-learning-producer-1] org.apache.kafka.clients.NetworkClient   : [Producer clientId=workplace-learning-producer-1] Node -1 disconnected.
2025-06-28T13:49:35.383+05:30  INFO 9568 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 4 class path changes (0 additions, 0 deletions, 4 modifications)
2025-06-28T13:49:35.389+05:30  INFO 9568 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-28T13:49:35.389+05:30  INFO 9568 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751098775389, current=DOWN, previous=UP]
2025-06-28T13:49:35.395+05:30  INFO 9568 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751098775395, current=UP, previous=DOWN]
2025-06-28T13:49:35.396+05:30  INFO 9568 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-28T13:49:35.413+05:30  INFO 9568 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-28T13:49:35.568+05:30  INFO 9568 --- [workplace-learning] [Thread-1] o.a.k.clients.producer.KafkaProducer     : [Producer clientId=workplace-learning-producer-1] Closing the Kafka producer with timeoutMillis = 30000 ms.
2025-06-28T13:49:35.580+05:30  INFO 9568 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Metrics scheduler closed
2025-06-28T13:49:35.582+05:30  INFO 9568 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-28T13:49:35.582+05:30  INFO 9568 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-06-28T13:49:35.583+05:30  INFO 9568 --- [workplace-learning] [Thread-1] o.apache.kafka.common.metrics.Metrics    : Metrics reporters closed
2025-06-28T13:49:35.584+05:30  INFO 9568 --- [workplace-learning] [Thread-1] o.a.kafka.common.utils.AppInfoParser     : App info kafka.producer for workplace-learning-producer-1 unregistered
2025-06-28T13:49:35.615+05:30  INFO 9568 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-28T13:49:35.621+05:30  INFO 9568 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-28T13:49:35.628+05:30  INFO 9568 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-28T13:49:35.632+05:30  INFO 9568 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-28T13:49:38.646+05:30  INFO 9568 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-28T13:49:38.685+05:30  INFO 9568 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-28T13:49:38.689+05:30  INFO 9568 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-28T13:49:39.130+05:30  INFO 9568 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-28T13:49:39.573+05:30  INFO 9568 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 9568 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-28T13:49:39.577+05:30  INFO 9568 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-28T13:49:48.166+05:30  INFO 9568 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-28T13:49:50.443+05:30  INFO 9568 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2270 ms. Found 31 JPA repository interfaces.
2025-06-28T13:49:52.547+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0a898feb-cb30-3132-b195-babb9ec03f8f
2025-06-28T13:49:53.976+05:30  WARN 9568 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-28T13:49:53.995+05:30  WARN 9568 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-28T13:49:54.031+05:30  WARN 9568 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-28T13:49:54.059+05:30  WARN 9568 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-28T13:49:54.105+05:30  WARN 9568 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-28T13:49:55.239+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-28T13:49:55.250+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-28T13:49:55.252+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-28T13:49:55.465+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-28T13:49:55.466+05:30  INFO 9568 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 15858 ms
2025-06-28T13:49:55.933+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-28T13:49:56.228+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@20b08418
2025-06-28T13:49:56.230+05:30  INFO 9568 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-28T13:49:56.232+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-28T13:49:57.121+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-28T13:49:57.158+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-28T13:49:57.345+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-28T13:50:02.244+05:30  INFO 9568 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-28T13:50:29.707+05:30  INFO 2724 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 2724 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-28T13:50:29.710+05:30  INFO 2724 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-28T13:50:29.839+05:30  INFO 2724 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-28T13:50:29.841+05:30  INFO 2724 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-28T13:50:31.818+05:30  INFO 2724 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-28T13:50:32.131+05:30  INFO 2724 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 303 ms. Found 31 JPA repository interfaces.
2025-06-28T13:50:32.555+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=0a898feb-cb30-3132-b195-babb9ec03f8f
2025-06-28T13:50:33.165+05:30  WARN 2724 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-28T13:50:33.170+05:30  WARN 2724 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-28T13:50:33.178+05:30  WARN 2724 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-28T13:50:33.184+05:30  WARN 2724 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-28T13:50:33.189+05:30  WARN 2724 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-28T13:50:33.772+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-28T13:50:33.790+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-28T13:50:33.791+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-28T13:50:33.878+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-28T13:50:33.879+05:30  INFO 2724 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4037 ms
2025-06-28T13:50:34.061+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-28T13:50:34.304+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@7b0021e4
2025-06-28T13:50:34.307+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-28T13:50:34.324+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-28T13:50:34.796+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-28T13:50:34.926+05:30  INFO 2724 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-28T13:50:34.998+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-28T13:50:36.445+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-28T13:50:47.651+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-28T13:50:52.898+05:30  INFO 2724 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-28T13:50:54.590+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-28T13:51:00.724+05:30  WARN 2724 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-28T13:51:01.057+05:30  WARN 2724 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-28T13:51:01.311+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-28T13:51:01.855+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-28T13:51:08.851+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-28T13:51:10.814+05:30  INFO 2724 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-28T13:51:10.899+05:30  WARN 2724 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-28T13:51:11.020+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-28T13:51:11.063+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-28T13:51:11.068+05:30  INFO 2724 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T13:51:11.082+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-28T13:51:11.082+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-28T13:51:11.083+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-28T13:51:11.084+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-28T13:51:11.084+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-28T13:51:11.085+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-28T13:51:11.085+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-28T13:51:11.445+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-28T13:51:11.448+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-28T13:51:11.451+05:30  INFO 2724 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-28T13:51:11.453+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1751098871453 with initial instances count: 6
2025-06-28T13:51:11.466+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-28T13:51:11.469+05:30  INFO 2724 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1751098871469, current=UP, previous=STARTING]
2025-06-28T13:51:11.469+05:30  INFO 2724 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-28T13:51:11.469+05:30  WARN 2724 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-28T13:51:11.487+05:30  INFO 2724 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-28T13:51:11.490+05:30  INFO 2724 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-28T13:51:11.536+05:30  INFO 2724 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 42.648 seconds (process running for 43.558)
2025-06-28T13:51:11.537+05:30  INFO 2724 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-28T13:51:49.016+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-28T13:51:49.016+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-28T13:51:49.021+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-06-28T13:51:49.159+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-1] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T13:56:11.089+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T13:59:24.222+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-3] h.w.w.c.PreApprovalApplicationController : Fetching applications with criteria - role: null, userId: null, companyId: null, status: null, state: null, search: null
2025-06-28T13:59:24.223+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Service: Fetching applications with criteria - role: null, userId: null, companyId: null, status: null, state: null, assignee: null, search: null
2025-06-28T13:59:24.226+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-3] .w.s.i.PreApprovalApplicationServiceImpl : Finding applications with specification and pagination
2025-06-28T13:59:25.531+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-3] w.r.PreApprovalApplicationRepositoryImpl : Status counts with base specification: {underReview=19, approved=13, pendingVetting=24, awaitingChanges=3, vettingOngoing=9, rejected=11, totalApplications=86, pendingApproval=0}
2025-06-28T14:00:55.276+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-6] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 91ede71f-6cbc-491d-b782-f687deec43dc
2025-06-28T14:01:11.103+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T14:01:32.034+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Processing create/update request for pre-approval application, isDraft: false
2025-06-28T14:01:32.034+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Creating new application
2025-06-28T14:01:32.036+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Creating new pre-approval application
2025-06-28T14:01:32.044+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : State is now SUBMITTED and status is now PENDING
2025-06-28T14:01:32.045+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Generated VAT number: BW-D400C9
2025-06-28T14:01:32.068+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Pre-approval application created with ID: 2c2daa72-5e37-42b2-8e5e-0eb90178e60e
2025-06-28T14:01:32.151+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : New application created successfully with ID: 2c2daa72-5e37-42b2-8e5e-0eb90178e60e
2025-06-28T14:01:32.219+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-7] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 2c2daa72-5e37-42b2-8e5e-0eb90178e60e
2025-06-28T14:01:33.753+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Workflow response: {message=Process started and application data retrieved successfully, success=true, applicationData={metadata={applicationNumber=APP-20250628-E8B181, referenceNumber=REF-20250628-10AF65, state=SUBMITTED, lastModified=2025-06-28T08:31:32.052+00:00, applicationId=2c2daa72-5e37-42b2-8e5e-0eb90178e60e, status=PENDING}, attachments=[], application={id=2c2daa72-5e37-42b2-8e5e-0eb90178e60e, userId=4b25a526-fd3a-4449-8e00-8d50a6ea258e, organisationId=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, company={uuid=bc0d3baa-639b-4df8-80f9-82cdeebd29d7, name=BUAN, type=EDUCATION_TRAINING_PROVIDER_LEVY_PAYER, industry=Agriculture, category=GENERAL_EDUCATION, physicalAddress=plot 123, Gwest, Gaborone, telephoneNumber=+**********, faxNumber=+**********, pepPipStatus=false, pepPipAssociateStatus=false, contactPerson={id=1743080519658, uuid=4b25a526-fd3a-4449-8e00-8d50a6ea258e, name=Bonolo, position=Secretary, mobileNumber=+26778951365, email=<EMAIL>}, employees=[{uuid=1d662764-788a-440f-a761-a5b02892275f, firstName=Gon, lastName=Dra, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=1500}, {uuid=27654f7d-244a-4ccd-84c7-df237021e088, firstName=Tha, lastName=Biso, idNumber=*********, idType=OMANG, idVerificationStatus=UNVERIFIED, gender=MALE, contactNumber=+26774503261, basicSalary=150}, {uuid=e2c55990-3316-42fa-905f-467273c88d53, firstName=Ying, lastName=Yang, idNumber=*********, idType=PASSPORT, idVerificationStatus=UNVERIFIED, gender=FEMALE, contactNumber=+26774503261, basicSalary=3400}], registrationApplication={uuid=c206e9cb-6485-4c40-9856-5461c7bc08c2, referenceNumber=APP-20250327-5032E6, status=APPROVED, state=SUBMITTED, applicationSubmissionDate=2025-03-27T13:01:59.663446, actionsRequested=[{uuid=38b6130b-9772-40cb-b87a-7fbb774f40b4, actionType=VERIFY_CIPA_NUMBER, actionMessage=Company CIPA Number verification pending, actionStatus=PENDING}], agentLead=null, agent=unassigned, manager=null}, verifications=[{uuid=9c9616c3-48ae-4de2-9089-3552bf4f93cf, reference=BW86165236462, type=COMPANY_NUMBER, status=UNVERIFIED, verifiedAt=null, expiryDate=null}]}, status=PENDING, state=SUBMITTED, applicationNumber=APP-20250628-E8B181, referenceNumber=REF-20250628-10AF65, reasonForTraining=employees., courseTitle=Data, trainingProvider=Institute, cityOfTraining=Gaborone, trainingStartDate=2024-01-01, trainingEndDate=2024-03-07, particularOfTrainings=[{id=ef654913-bc32-49d7-93b2-f3256e8950c7, productRelatedTraining=true, outsourcedTraining=false, nonCitizenEmployee=true, isTrainingLocal=true}], moduleDetails=[{id=c66bd200-89a7-4f69-bd35-40c4bbceff65, moduleName=Data Analysis1, expectedCompetencies=Ability to analyze large datasets, moduleReferenceId=null}, {id=4fc86531-c767-4b55-90e5-35b1d4af5aee, moduleName=Machine Learning1, expectedCompetencies=Ability to build predictive models, moduleReferenceId=null}], estimatedTrainingCosts=[{id=35d8f256-b2a2-4baf-98a4-d805397c52c6, itemDescription=Course fees, amount=5000.0}, {id=22acaceb-02f4-4e28-a155-b5ea56a16de1, itemDescription=Travel expenses, amount=1000.0}], subtotal=6000.0, total=6839.999999999999, vatNumber=BW-D400C9, accreditationEvidence=Certificate from ABC Training Institute, employeeIds=[0f97931c-a8b8-4fa5-9fe7-d7398f4a479b, 709069f7-2ae4-4578-b562-030056aa97b1], assignedAgent=null, assignedAgentLead=null, assignedOfficerLead=null, assignedOfficer=null, assignedManager=null, createdDate=2025-06-28T08:31:32.052+00:00, lastModifiedDate=2025-06-28T08:31:32.052+00:00, createdBy=null, lastModifiedBy=null, totalLearningHours=null, paperlessId=null, attachments=null}}, processInstanceId=4b803cd4-53fa-11f0-b48d-00155d4133f2}
2025-06-28T14:01:33.782+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-4] .w.s.i.PreApprovalApplicationServiceImpl : Process instance ID 4b803cd4-53fa-11f0-b48d-00155d4133f2 saved for application 2c2daa72-5e37-42b2-8e5e-0eb90178e60e
2025-06-28T14:01:33.783+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-4] h.w.w.c.PreApprovalApplicationController : Process instance ID 4b803cd4-53fa-11f0-b48d-00155d4133f2 saved for application 2c2daa72-5e37-42b2-8e5e-0eb90178e60e
2025-06-28T14:01:39.595+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-8] h.w.w.c.PreApprovalApplicationController : Updating paperlessId for application ID: 2c2daa72-5e37-42b2-8e5e-0eb90178e60e
2025-06-28T14:01:39.596+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-8] .w.s.i.PreApprovalApplicationServiceImpl : Updating paperlessId 162 for application 2c2daa72-5e37-42b2-8e5e-0eb90178e60e
2025-06-28T14:01:39.606+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-8] .w.s.i.PreApprovalApplicationServiceImpl : Paperless ID 162 saved for application 2c2daa72-5e37-42b2-8e5e-0eb90178e60e
2025-06-28T14:02:27.141+05:30  INFO 2724 --- [workplace-learning] [http-nio-8091-exec-9] .w.s.i.PreApprovalApplicationServiceImpl : Fetching pre-approval application by ID: 2c2daa72-5e37-42b2-8e5e-0eb90178e60e
2025-06-28T14:06:11.116+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T14:11:11.119+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T14:16:11.133+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T14:21:11.141+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T14:26:11.147+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T14:31:11.162+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T14:36:11.168+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T14:41:11.174+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T14:46:11.188+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-28T14:51:11.198+05:30  INFO 2724 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
