package co.bw.hrdc.weblogic.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import co.bw.hrdc.weblogic.config.FeignConfig;
import co.bw.hrdc.weblogic.util.ApiResponse;


@FeignClient(name = "WORKPLACE-LEARNING", fallback = WorkplaceLearningClientFallback.class, configuration = FeignConfig.class)
public interface WorkplaceLearningClient {

    @GetMapping("/api/v1/ncbsc/applications/recognition/{referenceNumber}")
    ApiResponse<?> getApplicationByReferenceNumber(@PathVariable String referenceNumber);

    @GetMapping("/api/v1/pre-approval-applications/application-number/{applicationId}")
    ApiResponse<?> getApplicationById(@PathVariable String applicationId);

    @PutMapping("/api/v1/pre-approval-applications/{applicationId}/paperless-id")
    ApiResponse<?> updatePaperlessIdByApplicationId(@PathVariable String applicationId, @RequestBody java.util.Map<String, String> payload);


    @PutMapping("/api/v1/ncbsc/workplace-training-plan/{applicationId}/email-pdf-attachments")
    ApiResponse<?> updatePaperlessIdToTrainingPlan(@PathVariable String applicationId, @RequestBody java.util.Map<String, String> payload);
} 

