server:
  port: 8089

spring:
  application:
    name: communication
  # Database configuration
  datasource:
    url: **********************************************
    username: postgres
    password: root
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
      validation-timeout: 5000
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  # Kafka configuration
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  # Sleuth and Zipkin configuration
  sleuth:
    sampler:
      probability: 1.0
  zipkin:
    base-url: http://localhost:9411/
  # mail:
  #   host: smtp.gmail.com
  #   port: 587 
  #   username: a<PERSON><PERSON><PERSON><PERSON><EMAIL>
  #   password: nemhnnpqjqfdbhup
  #   properties:
  #     mail:
  #       smtp:
  #         auth: true
  #         starttls:
  #           enable: true
  #   protocol: smtp

    # Mail configuration - using Mailtrap
  mail:
    host: sandbox.smtp.mailtrap.io
    port: 2525
    username: b12ead1734f21a
    password: 7d862c1fbcda59
    protocol: smtp
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    gateway:
      enabled: true
  info:
    env:
      enabled: true

info:
  app:
    name: "communication"
    description: "HRDC communication Application"
    version: "1.0.0"

eureka:
  instance:
    preferIpAddress: true
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: http://localhost:8070/eureka/

# Feign configuration for Eureka discovery
feign:
  client:
    defaultToProperties: true
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000
  circuitbreaker:
  enabled: true

# logging:
#   level:
#     org.apache.kafka: WARN
#     org.springframework.kafka: INFO
#     co.bw.hrdc.weblogic.kafka: DEBUG
#     co.bw.hrdc.weblogic.emailsender: DEBUG
#     co.bw.hrdc.weblogic.config.FeignConfig: DEBUG
#     co.bw.hrdc.weblogic.api: DEBUG
#     com.netflix.discovery: DEBUG
#     com.netflix.eureka: DEBUG
#     org.springframework.cloud.openfeign: DEBUG
#     feign: DEBUG
#     feign.hystrix: DEBUG