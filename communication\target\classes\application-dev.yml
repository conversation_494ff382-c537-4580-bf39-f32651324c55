server:
  port: 8089

spring:
  application:
    name: communication
  # Database configuration
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:communication}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:administrator}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
  jpa:
    hibernate:
      ddl-auto: update  #'none', 'validate', 'update', 'create', or 'create-drop'
    show-sql: true  # Set to true to log the SQL queries to the console
  # Kafka configuration
  kafka:
    bootstrap-servers: ${KAFKA_SERVER:localhost:9094}
    consumer:
      group-id: email-service-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  # Sleuth and <PERSON><PERSON>kin configuration
  sleuth:
    sampler:
      probability: 1.0
  zipkin:
    base-url: ${ZIPKIN_BASE_URL:http://localhost:9411/}
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: tfnxxooglueqgcjm
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
    protocol: smtp

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    gateway:
      enabled: true
  info:
    env:
      enabled: true

info:
  app:
    name: "communication"
    description: "HRDC communication Application"
    version: "1.0.0"

eureka:
  instance:
    preferIpAddress: true
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: ${EUREKA_DEFAULT_ZONE:http://localhost:8070/eureka/}

# Feign configuration for Eureka discovery
feign:
  client:
    defaultToProperties: true
    config:
      default:
        connect-timeout: 10000
        read-timeout: 60000
  circuitbreaker:
  enabled: true


