package co.bw.hrdc.weblogic.emailsender.service;
import co.bw.hrdc.weblogic.api.DmsClient;
import co.bw.hrdc.weblogic.api.WorkplaceLearningClient;
import co.bw.hrdc.weblogic.delegate.FetchDataDelegate;
import co.bw.hrdc.weblogic.dto.NcbscApplicationDto;
import co.bw.hrdc.weblogic.emailsender.dto.EmailRequest;
import co.bw.hrdc.weblogic.util.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Service
public class EmailService {
    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);
    
    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;
    private final PdfService pdfService;
    private final FetchDataDelegate fetchDataDelegate;
    private final DmsClient dmsClient;
    private final WorkplaceLearningClient workplaceLearningClient;
    
    @Value("${spring.mail.username:<EMAIL>}")
    private String defaultFromEmail;

    public EmailService(JavaMailSender mailSender, TemplateEngine templateEngine, PdfService pdfService, FetchDataDelegate fetchDataDelegate, DmsClient dmsClient, WorkplaceLearningClient workplaceLearningClient) {
        this.mailSender = mailSender;
        this.templateEngine = templateEngine;
        this.pdfService = pdfService;
        this.fetchDataDelegate = fetchDataDelegate;
        this.dmsClient = dmsClient;
        this.workplaceLearningClient = workplaceLearningClient;
    }

    public void sendEmail(String emailJson) {
        try {
            logger.debug("Processing email request: {}", emailJson);
            
            // Parse JSON message
            EmailRequest emailRequest = new ObjectMapper().readValue(emailJson, EmailRequest.class);
            
            // Check and set recipient email address
            if (emailRequest.getContactAddress() == null || emailRequest.getContactAddress().isEmpty()) {
                if (emailRequest.getToMail() != null && !emailRequest.getToMail().isEmpty()) {
                    emailRequest.setContactAddress(emailRequest.getToMail());
                } else if (emailRequest.getTo() != null && !emailRequest.getTo().isEmpty()) {
                    emailRequest.setContactAddress(emailRequest.getTo());
                } else {
                    logger.error("Email recipient address is missing in the request");
                    return;
                }
            }

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = getMimeMessageHelper(message, emailRequest);

            Context context = new Context();
            context.setVariable("name", emailRequest.getName());
            
            // Use body or message field for the email content
            String emailContent = emailRequest.getBody();
            if (emailContent == null || emailContent.isEmpty()) {
                emailContent = emailRequest.getMessage() != null ? emailRequest.getMessage() : "";
            }
            
            context.setVariable("message", emailContent);

            String htmlContent = templateEngine.process("email-template", context);
            helper.setText(htmlContent, true);

            logger.debug("Sending email to: {}", emailRequest.getContactAddress());
            mailSender.send(message);
            logger.info("Email sent successfully to: {}", emailRequest.getContactAddress());
        } catch (Exception e) {
            logger.error("Failed to send email: {}", e.getMessage());
            e.printStackTrace();
        }
    }

    @NotNull
    private MimeMessageHelper getMimeMessageHelper(MimeMessage message, EmailRequest emailRequest) throws MessagingException, IOException {
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setTo(emailRequest.getContactAddress());

        boolean shouldAttachPdf = false;
        
        // Determine the subject
        if (emailRequest.getOtpType() != null && !emailRequest.getOtpType().isEmpty()) {
            // Use OTP type to determine subject
            if (emailRequest.getOtpType().equals("LOGIN_VERIFICATION")) {
                helper.setSubject("Your 2fa password");
            } else if (emailRequest.getOtpType().equals("ACCOUNT_VERIFICATION")) {
                helper.setSubject("Account Verification");
            } else {
                helper.setSubject("OTP Number");
            }
        } else if (emailRequest.getApplicationType().equalsIgnoreCase("RENEWAL")) {
            helper.setSubject(emailRequest.getSubject() != null ? emailRequest.getSubject() : "HRDC Renewal Notification");
        } else {
            // Default subject
            helper.setSubject("HRDC");
            String applicationStatus = emailRequest.getApplicationStatus();
            String actionBy = emailRequest.getActionBy();
            
            // Check for different roles and actions
            if (actionBy != null) {
                if (actionBy.equalsIgnoreCase("MANAGER") && applicationStatus != null && applicationStatus.equalsIgnoreCase("APPROVED")) {
                    // Only MANAGER can send approval letters
                    shouldAttachPdf = true;
                } else if ((actionBy.equalsIgnoreCase("MANAGER") || actionBy.equalsIgnoreCase("AGENT") || actionBy.equalsIgnoreCase("OFFICER")) 
                          && applicationStatus != null && applicationStatus.equalsIgnoreCase("REJECTED")) {
                    // MANAGER, AGENT, or OFFICER can send rejection letters
                    shouldAttachPdf = true;
                } else if (actionBy.equalsIgnoreCase("AGENT_LEAD")) {
                    // AGENTLEAD sends submission confirmation letters
                    shouldAttachPdf = true;
                
                } else {
                    System.out.println("No PDF attachment criteria met for actionBy: " + actionBy + " with status: " + applicationStatus);
                }
            } else {
                System.out.println("actionBy is null, no PDF will be attached");
            }
            
           
            if (shouldAttachPdf) {
                String applicationType = emailRequest.getApplicationType();
                String applicationNumber = emailRequest.getApplicationNumber();
                
                // Handle different scenarios based on actionBy and applicationStatus
                if (applicationType.equalsIgnoreCase("RECOGNITION")) {
                    logger.info("Processing RECOGNITION application: {}, status: {}, actionBy: {}", applicationNumber, applicationStatus, actionBy);
                    
                    if (actionBy.equalsIgnoreCase("MANAGER") && applicationStatus.equalsIgnoreCase("APPROVED")) {
                        // Only MANAGER can send approval letters
                        NcbscApplicationDto ncbscApplicationDto = fetchDataDelegate.fetchData(applicationNumber);
                        ByteArrayOutputStream pdfStream = pdfService.createPdf(ncbscApplicationDto);
                        
                        String baseFilename = "HRDC_Recognition_Letter.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_Recognition_Letter_" + timestamp + ".pdf";
                        String documentTitle = "Recognition Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "RecognitionAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if ((actionBy.equalsIgnoreCase("MANAGER") || actionBy.equalsIgnoreCase("AGENT") || actionBy.equalsIgnoreCase("OFFICER")) 
                              && applicationStatus.equalsIgnoreCase("REJECTED")) {
                        // MANAGER, AGENT, or OFFICER can send rejection letters
                        ByteArrayOutputStream pdfStream = pdfService.createRejectedNcbscPdf();
                        
                        String baseFilename = "HRDC_Recognition_Letter_Rejected.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_Recognition_Letter_Rejected_" + timestamp + ".pdf";
                        String documentTitle = "Rejected Recognition Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "RecognitionAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy,applicationStatus);
                        
                    } else if (actionBy.equalsIgnoreCase("AGENT_LEAD")) {
                        // AGENTLEAD sends submission confirmation letters
                        ByteArrayOutputStream pdfStream = pdfService.createApplicationSubmissionPdf("RECOGNITION");
                        
                        String baseFilename = "HRDC_Recognition_Submission_Confirmation.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_Recognition_Submission_Confirmation_" + timestamp + ".pdf";
                        String documentTitle = "Recognition Submission Confirmation for " + applicationNumber + " - " + timestamp;
                        String serviceName = "RecognitionAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if (actionBy.equalsIgnoreCase("MANAGER") && applicationStatus.equalsIgnoreCase("CHANGE_REQUEST")) {
                        // MANAGER can send change request letters
                        ByteArrayOutputStream pdfStream = pdfService.createChangeRequestPdf();
                        
                        String baseFilename = "HRDC_Recognition_Letter_ChangeRequest.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_Recognition_Letter_ChangeRequest_" + timestamp + ".pdf";
                        String documentTitle = "Change Request Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "RecognitionAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                    }
                    
                } else if (applicationType.equalsIgnoreCase("PRE_APPROVAL")) {
                    logger.info("Processing PRE_APPROVAL application: {}, status: {}, actionBy: {}", applicationNumber, applicationStatus, actionBy);
                    
                    if (actionBy.equalsIgnoreCase("MANAGER") && applicationStatus.equalsIgnoreCase("APPROVED")) {
                        // Only MANAGER can send approval letters
                        ByteArrayOutputStream pdfStream = pdfService.createPreApprovalApprovedPdf();
                        
                        String baseFilename = "HRDC_PreApproval_Letter_Approved.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_PreApproval_Letter_Approved_" + timestamp + ".pdf";
                        String documentTitle = "Approved Pre-Approval Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "PreApprovalAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if ((actionBy.equalsIgnoreCase("MANAGER") || actionBy.equalsIgnoreCase("AGENT") || actionBy.equalsIgnoreCase("OFFICER")) 
                              && applicationStatus.equalsIgnoreCase("REJECTED")) {
                        // MANAGER, AGENT, or OFFICER can send rejection letters
                        ByteArrayOutputStream pdfStream = pdfService.createPreApprovalRejectedPdf();
                        
                        String baseFilename = "HRDC_PreApproval_Letter_Rejected.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_PreApproval_Letter_Rejected_" + timestamp + ".pdf";
                        String documentTitle = "Rejected Pre-Approval Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "PreApprovalAttachment";
                        
                        // Attach to email and upload to DMS
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if (actionBy.equalsIgnoreCase("AGENT_LEAD")) {
                        // AGENTLEAD sends submission confirmation letters
                        ByteArrayOutputStream pdfStream = pdfService.createApplicationSubmissionPdf("PRE_APPROVAL");
                        
                        String baseFilename = "HRDC_PreApproval_Submission_Confirmation.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_PreApproval_Submission_Confirmation_" + timestamp + ".pdf";
                        String documentTitle = "Pre-Approval Submission Confirmation for " + applicationNumber + " - " + timestamp;
                        String serviceName = "PreApprovalAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if (actionBy.equalsIgnoreCase("MANAGER") && applicationStatus.equalsIgnoreCase("CHANGE_REQUEST")) {
                        // MANAGER can send change request letters
                        ByteArrayOutputStream pdfStream = pdfService.createPreApprovalChangeRequestPdf();
                        
                        String baseFilename = "HRDC_PreApproval_Letter_ChangeRequest.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_PreApproval_Letter_ChangeRequest_" + timestamp + ".pdf";
                        String documentTitle = "Change Request Pre-Approval Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "PreApprovalAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName ,actionBy, applicationStatus);
                    }
                    
                } else if (applicationType.equalsIgnoreCase("WORK_SKILLS")) {
                    logger.info("Processing WORK_SKILLS application: {}, status: {}, actionBy: {}", applicationNumber, applicationStatus, actionBy);
                    
                    if (actionBy.equalsIgnoreCase("MANAGER") && applicationStatus.equalsIgnoreCase("APPROVED")) {
                        // Only MANAGER can send approval letters
                        
                        ByteArrayOutputStream pdfStream = pdfService.createWorkSkillApprovedPdf();
                        
                        
                        String baseFilename = "HRDC_WorkSkills_Letter_Approved.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_WorkSkills_Letter_Approved_" + timestamp + ".pdf";
                        String documentTitle = "Approved Work Skills Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "WorkSkillsAttachment";
                        
                        // Attach to email and upload to DMS
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if ((actionBy.equalsIgnoreCase("MANAGER") || actionBy.equalsIgnoreCase("AGENT") || actionBy.equalsIgnoreCase("OFFICER")) 
                              && applicationStatus.equalsIgnoreCase("REJECTED")) {
                        // MANAGER, AGENT, or OFFICER can send rejection letters
                        ByteArrayOutputStream pdfStream = pdfService.createWorkSkillRejectedPdf();
                        
                        String baseFilename = "HRDC_WorkSkills_Letter_Rejected.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_WorkSkills_Letter_Rejected_" + timestamp + ".pdf";
                        String documentTitle = "Rejected Work Skills Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "WorkSkillsAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if (actionBy.equalsIgnoreCase("AGENT_LEAD")) {
                        // AGENTLEAD sends submission confirmation letters
                        ByteArrayOutputStream pdfStream = pdfService.createApplicationSubmissionPdf("WORK_SKILLS");
                        
                        String baseFilename = "HRDC_WorkSkills_Submission_Confirmation.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_WorkSkills_Submission_Confirmation_" + timestamp + ".pdf";
                        String documentTitle = "Work Skills Submission Confirmation for " + applicationNumber + " - " + timestamp;
                        String serviceName = "WorkSkillsAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if (actionBy.equalsIgnoreCase("MANAGER") && applicationStatus.equalsIgnoreCase("CHANGE_REQUEST")) {
                        // MANAGER can send change request letters
                        ByteArrayOutputStream pdfStream = pdfService.createWorkSkillChangeRequestPdf();
                        
                        String baseFilename = "HRDC_WorkSkills_Letter_ChangeRequest.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_WorkSkills_Letter_ChangeRequest_" + timestamp + ".pdf";
                        String documentTitle = "Change Request Work Skills Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "WorkSkillsAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                    }
                    
                } else if (applicationType.equalsIgnoreCase("NOC")) {
                    logger.info("Processing NOC application: {}, status: {}, actionBy: {}", applicationNumber, applicationStatus, actionBy);
                    
                    if (actionBy.equalsIgnoreCase("MANAGER") && applicationStatus.equalsIgnoreCase("APPROVED")) {
                        // Only MANAGER can send approval letters
                        ByteArrayOutputStream pdfStream = pdfService.createNocApprovedPdf();
                        
                        String baseFilename = "HRDC_NOC_Letter_Approved.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_NOC_Letter_Approved_" + timestamp + ".pdf";
                        String documentTitle = "Approved NOC Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "NOCAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if ((actionBy.equalsIgnoreCase("MANAGER") || actionBy.equalsIgnoreCase("AGENT") || actionBy.equalsIgnoreCase("OFFICER")) 
                              && applicationStatus.equalsIgnoreCase("REJECTED")) {
                        // MANAGER, AGENT, or OFFICER can send rejection letters
                        ByteArrayOutputStream pdfStream = pdfService.createNocRejectedPdf();
                        
                        String baseFilename = "HRDC_NOC_Letter_Rejected.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_NOC_Letter_Rejected_" + timestamp + ".pdf";
                        String documentTitle = "Rejected NOC Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "NOCAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if (actionBy.equalsIgnoreCase("AGENT_LEAD")) {
                        // AGENTLEAD sends submission confirmation letters
                        ByteArrayOutputStream pdfStream = pdfService.createApplicationSubmissionPdf("NOC");
                        
                        String baseFilename = "HRDC_NOC_Submission_Confirmation.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_NOC_Submission_Confirmation_" + timestamp + ".pdf";
                        String documentTitle = "NOC Submission Confirmation for " + applicationNumber + " - " + timestamp;
                        String serviceName = "NOCAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                        
                    } else if (actionBy.equalsIgnoreCase("MANAGER") && applicationStatus.equalsIgnoreCase("CHANGE_REQUEST")) {
                        // MANAGER can send change request letters
                        ByteArrayOutputStream pdfStream = pdfService.createNocChangeRequestPdf();
                        
                        String baseFilename = "HRDC_NOC_Letter_ChangeRequest.pdf";
                        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                        String filenameWithTimestamp = "HRDC_NOC_Letter_ChangeRequest_" + timestamp + ".pdf";
                        String documentTitle = "Change Request NOC Letter for " + applicationNumber + " - " + timestamp;
                        String serviceName = "NOCAttachment";
                        
                        attachAndUploadDocument(helper, pdfStream, filenameWithTimestamp, baseFilename, 
                                               documentTitle, applicationNumber, serviceName, actionBy, applicationStatus);
                    }
                    
                } else {
                    // Default case for unknown application types
                    logger.info("Unknown application type: {}", applicationType);
                    helper.setSubject("HRDC");
                    logger.debug("No PDF attachment added to email for unknown application type");
                }
               
            }
        }
        
        // Set from address if configured
        try {
            helper.setFrom(defaultFromEmail);
        } catch (Exception e) {
            logger.warn("Could not set from address: {}", e.getMessage());
            // Continue without setting from address, as it might be set by the mail server
        }
        
        
        return helper;
    }

    /**
     * Extracts a numeric ID from an application number or generates a hash code
     * @param applicationNumber The application number string
     * @return An integer that can be used as an archive serial number
     */
    private Integer extractNumericId(String applicationNumber) {
        if (applicationNumber == null || applicationNumber.isEmpty()) {
            // Generate a random positive integer if no application number
            return Math.abs(new java.util.Random().nextInt(900000) + 100000);
        }
        
        try {
            // Try to extract numeric parts from the application number
            String numericPart = applicationNumber.replaceAll("[^0-9]", "");
            if (!numericPart.isEmpty()) {
                // If the numeric part is too long for an integer, use the last 9 digits
                if (numericPart.length() > 9) {
                    numericPart = numericPart.substring(numericPart.length() - 9);
                }
                return Integer.parseInt(numericPart);
            }
        } catch (NumberFormatException e) {
            logger.warn("Could not parse numeric ID from application number: {}", applicationNumber);
        }
        
        // If no numeric part or parsing failed, use the hash code (ensuring it's positive)
        return Math.abs(applicationNumber.hashCode());
    }
    
    /**
     * Helper method to attach a document to an email and upload it to DMS
     * @param helper MimeMessageHelper for email attachment
     * @param pdfStream PDF content as ByteArrayOutputStream
     * @param filenameWithTimestamp Filename with timestamp for email attachment
     * @param baseFilename Base filename for DMS upload
     * @param documentTitle Title for the document in DMS
     * @param applicationNumber Application number for reference
     * @param serviceName Service name for DMS upload
     * @throws MessagingException If there's an error with the email messaging
     */
    private void attachAndUploadDocument(MimeMessageHelper helper, ByteArrayOutputStream pdfStream, 
                                        String filenameWithTimestamp, String baseFilename, 
                                        String documentTitle, String applicationNumber, String serviceName, String actionBy, String applicationStatus) 
                                        throws MessagingException {
        try {
            // Attach to email
            ByteArrayResource pdfResource = new ByteArrayResource(pdfStream.toByteArray());
            helper.addAttachment(filenameWithTimestamp, pdfResource);
            
            // Convert to MultipartFile for DMS upload
            MultipartFile multipartFile = createMultipartFile(pdfStream.toByteArray(), baseFilename);
            
            // Upload to DMS
            uploadDocumentToDms(multipartFile, documentTitle, applicationNumber, serviceName,actionBy, applicationStatus);
            
            logger.debug("PDF attachment added to email with filename: {}", filenameWithTimestamp);
        } catch (Exception e) {
            logger.error("Error attaching document to email: {}", e.getMessage());
            logger.debug("Attachment error details", e);
            // Continue with email sending even if attachment fails
        }
    }
    
    /**
     * Helper method to upload a document to DMS with retry mechanism
     * @param multipartFile Document to upload
     * @param documentTitle Title for the document
     * @param applicationNumber Application number for reference
     * @param serviceName Service name for DMS
     */
    private void uploadDocumentToDms(MultipartFile multipartFile, String documentTitle, 
                                    String applicationNumber, String serviceName, String actionBy, String applicationStatus) {
        try {
            // Call uploadDocument with required parameters
            logger.info("Attempting to upload document to DMS: {}", documentTitle);
            
            // Log the document details before upload
            
            logger.info("Document details - Name: {}, Size: {}, ContentType: {}", 
                multipartFile.getOriginalFilename(), 
                multipartFile.getSize(), 
                multipartFile.getContentType());
            
            // Extract a numeric ID from the application number or generate one
            Integer archiveSerialNumber = extractNumericId(applicationNumber);
            logger.info("Using archive serial number: {} for application: {}", 
                archiveSerialNumber, applicationNumber);
            
            // Upload document to DMS
            ApiResponse<?> response = dmsClient.uploadDocument(
                multipartFile,                // document
                null,                         // documents
                documentTitle,                // title
                null,                         // created
                null,                         // correspondent
                null,                         // documentType
                null,                         // storagePath
                null,                         // tags
                null,          // archiveSerialNumber 
                null,                         // customFields
                serviceName,                         // serviceName
                null                          // subfolder
            );
            
            logger.info("DMS upload response: {}", response);
            
            if (response != null && response.isStatus()) {
                logger.info("Document successfully uploaded to DMS: {}", response.getMessage());
                logger.info("Document data: {}", response.getData());
                
                // Extract document ID from response
                Integer documentId = extractNumericId(response);
                
                if (documentId != null) {
                    logger.info("Successfully extracted document ID: {}", documentId);

                    if ((actionBy.equalsIgnoreCase("MANAGER") && applicationStatus.equalsIgnoreCase("APPROVED"))
                        || applicationStatus.equalsIgnoreCase("REJECTED")) {

                        // Store document ID as paperless ID for pre-approval application
                        if ("PreApprovalAttachment".equals(serviceName)) {
                            storePaperlessIdToPreApprovalApplication(applicationNumber, documentId.toString());
                        } else if ("WorkSkillsAttachment".equals(serviceName)) {
                            storePaperlessIdToWorkSkillsApplication(applicationNumber, documentId.toString());
                        }
                    }
                    
                } else {
                    logger.warn("Document ID is null or could not be extracted from response");
                }
            } else {
                logger.error("DMS upload returned unsuccessful response: {}", 
                    response != null ? response.getMessage() : "null response");
            }
        } catch (Exception e) {
            // Log the error but continue with email sending
            logger.error("Failed to upload document to DMS: {}", e.getMessage());
            logger.debug("DMS upload error details", e);
        }
    }
    
    /**
     * Extract paperless ID from DMS response with retry mechanism to handle stale data
     */
    private Integer extractNumericId(ApiResponse<?> response) {
        Integer documentId = null;

        try {
            Object responseData = response.getData();
            logger.debug("Response data: {}", responseData);

            // First try to extract from the new format with document_id field
            if (responseData instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) responseData;
                
                // Check for direct document_id field
                if (dataMap.containsKey("document_id")) {
                    Object docIdObj = dataMap.get("document_id");
                    if (docIdObj != null) {
                        try {
                            documentId = Integer.parseInt(docIdObj.toString());
                            logger.info("Extracted document_id directly: {}", documentId);
                            return documentId;
                        } catch (NumberFormatException e) {
                            logger.warn("Unable to parse document_id: {}", docIdObj);
                        }
                    }
                }
             
            }
        } catch (Exception e) {
            logger.error("Error extracting document ID: {}", e.getMessage());
            logger.debug("Error details", e);
        }

        return documentId;
    }

    /**
     * Helper method to convert a byte array to a MultipartFile
     * @param bytes The byte array to convert
     * @param filename The name of the file
     * @return A MultipartFile created from the byte array with timestamp in the filename
     */
    private MultipartFile createMultipartFile(final byte[] bytes, final String filename) {
        // Add timestamp to filename
        String fileNameWithoutExtension = filename;
        String extension = "";
        
        int dotIndex = filename.lastIndexOf('.');
        if (dotIndex > 0) {
            fileNameWithoutExtension = filename.substring(0, dotIndex);
            extension = filename.substring(dotIndex);
        }
        
        // Format current timestamp
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String filenameWithTimestamp = fileNameWithoutExtension + "_" + timestamp + extension;
        
        logger.info("Creating MultipartFile with timestamped filename: {}", filenameWithTimestamp);
        
        return new MultipartFile() {
            @Override
            public String getName() {
                return "document";
            }

            @Override
            public String getOriginalFilename() {
                return filenameWithTimestamp;
            }

            @Override
            public String getContentType() {
                return "application/pdf";
            }

            @Override
            public boolean isEmpty() {
                return bytes == null || bytes.length == 0;
            }

            @Override
            public long getSize() {
                return bytes == null ? 0 : bytes.length;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return bytes;
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return new ByteArrayInputStream(bytes);
            }

            @Override
            public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
                if (bytes == null) {
                    throw new IOException("No content available to transfer");
                }
                
                try (FileOutputStream fos = new FileOutputStream(dest)) {
                    fos.write(bytes);
                } catch (IOException e) {
                    logger.error("Failed to transfer file: {}", e.getMessage());
                    throw e;
                }
            }
        };
    }
    
    /**
     * Store paperless ID to Pre-Approval Application using Feign client
     * @param applicationNumber Application number to identify the application
     * @param paperlessId Paperless ID to store
     */
    private void storePaperlessIdToPreApprovalApplication(String applicationNumber, String paperlessId) {
        try {
            logger.info("Attempting to store paperless ID {} for application number: {}", paperlessId, applicationNumber);
                    
                String applicationId = applicationNumber;
                java.util.Map<String, String> payload = new java.util.HashMap<>();
                    payload.put("paperlessId", paperlessId);
                 
                    // Update the paperless ID
                    ApiResponse<?> updateResponse = workplaceLearningClient.updatePaperlessIdByApplicationId(applicationId, payload);
                    
                    if (updateResponse != null && updateResponse.isStatus()) {
                        logger.info("Successfully stored paperless ID {} for application ID: {}", paperlessId, applicationId);
                    } else {
                        logger.error("Failed to store paperless ID for application ID: {}. Response: {}", 
                            applicationId, updateResponse != null ? updateResponse.getMessage() : "null response");
                    }
            } catch (Exception e) {
            logger.error("Error storing paperless ID for application number {}: {}", applicationNumber, e.getMessage());
            logger.debug("Error details", e);
        }
    }

      private void storePaperlessIdToWorkSkillsApplication(String applicationNumber, String paperlessId) {
        try {
            logger.info("Attempting to store paperless ID {} for application number: {}", paperlessId, applicationNumber);
                    
                String applicationId = applicationNumber;
                java.util.Map<String, String> payload = new java.util.HashMap<>();
                    payload.put("paperlessId", paperlessId);
                 
                    // Update the paperless ID
                    ApiResponse<?> updateResponse = workplaceLearningClient.updatePaperlessIdToTrainingPlan(applicationId, payload);
                    
                    if (updateResponse != null && updateResponse.isStatus()) {
                        logger.info("Successfully stored paperless ID {} for application ID: {}", paperlessId, applicationId);
                    } else {
                        logger.error("Failed to store paperless ID for application ID: {}. Response: {}", 
                            applicationId, updateResponse != null ? updateResponse.getMessage() : "null response");
                    }
            } catch (Exception e) {
            logger.error("Error storing paperless ID for application number {}: {}", applicationNumber, e.getMessage());
            logger.debug("Error details", e);
        }
    }
}
